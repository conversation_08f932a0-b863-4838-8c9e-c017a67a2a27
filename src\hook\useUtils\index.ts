import Clipboard from 'clipboard'
import QRCode from './qrcode'
import { md5 } from 'md5js'
import { useEnvConfig } from '../useEnvConfig'
import { cloneDeep } from 'lodash'
import utils from './utils'

export type CurryKey = ((...keys: Array<string | number>) => CurryKey) & {
    key: string
    getChildKey: (...childKeys: Array<string | number>) => string
}

function loadUrlQuery(url?: string): { query: Record<string, any>; hashQuery: Record<string, any> } {
    function getObjFromStr(str: string) {
        return str
            .split('&')
            .map((item) => item.split('='))
            .reduce((pre, cur) => {
                pre[cur[0]] = unescape(decodeURIComponent(cur[1]))
                return pre
            }, {})
    }
    url = url || ''
    const query = {
        query: {},
        hashQuery: {}
    }
    let queryStr = url.split('#')[0].split('?')[1] || ''
    if (queryStr) {
        query.query = getObjFromStr(queryStr)
    }
    queryStr = (url.split('#')[1] || '').split('?')[1] || ''
    if (queryStr) {
        query.hashQuery = getObjFromStr(queryStr)
    }
    return query
}

function getUrlQuery(k: string, url?) {
    url = url || window.location.href
    const {query, hashQuery} = loadUrlQuery(url)
    return Object.assign({}, query, hashQuery)[k]
}

function copyText(text: string) {
    const dom = document.createElement('div')
    const clipboard = new Clipboard(dom, {
        text: () => text,
        action: () => 'copy'
    })
    dom.click()
}

/**
 * 深度合并对象
 * @param firstObj  被合并的对象
 * @param secondObj 合并进来的对象
 */
function deepObjectMerge(firstObj, secondObj) {
    const merge = (first, second) => {
        for (let key in second) {
            if (first[key] && first[key].toString() === '[object Object]') {
                first[key] = deepObjectMerge(first[key], second[key])
            } else {
                first[key] = second[key]
            }
        }
        return first
    }
    // 目的是为了避免传进来的firstObj被污染
    return merge(JSON.parse(JSON.stringify(firstObj)), secondObj)
}

function compareVersion(v1: string, v2: string) {
    let v1Arr = v1.split('.')
    let v2Arr = v2.split('.')
    const len = Math.max(v1Arr.length, v2Arr.length)

    while (v1Arr.length < len) {
        v1Arr.push('0')
    }
    while (v2Arr.length < len) {
        v2Arr.push('0')
    }

    for (let i = 0; i < len; i++) {
        const num1 = Number(v1Arr[i])
        const num2 = Number(v2Arr[i])

        if (num1 > num2) {
            return 1
        } else if (num1 < num2) {
            return -1
        }
    }

    return 0
}

function isValidUrl(url: string): boolean {
    // 完整的url链接
    try {
        new URL(url)
        return true
    } catch (error) {}
    return false
}

/**
 * 获取静态资源链接
 * @param url url链接
 * @returns 完整链接
 */
function getAssetsUrl(url: string): string {
    if (!url) {
        return url
    }
    // 完整的url链接
    if (isValidUrl(url)) {
        return url
    }
    // 先替换通配符
    if (url.indexOf('__public__') === 0) {
        url = url.replace('__public__', '')
    }
    const { ASSETS_URL } = useEnvConfig()
    return ASSETS_URL + url
}

/**
 * 柯里化生成key
 * 例子：
 * let fn = curryKey()
 * console.log(fn.key)              // ''
 * let fn2 = fn.getChildKey('a')
 * console.log(fn2)             // 'a'
 * let fn3 = fn('a', 'b')
 * console.log(fn3.key)             // 'a.b'
 * -------
 * let fn = curryKey('a')
 * console.log(fn.key)              // 'a'
 * let fn2 = fn.getChildKey('b')
 * console.log(fn2)             // 'a.b'
 * let fn3 = fn('c', 'd')
 * console.log(fn3.key)             // 'a.c.d'
 * -------
 * let fn = curryKey('a', 'b')
 * console.log(fn.key)              // 'a.b'
 * let fn2 = fn.getChildKey('b')
 * console.log(fn2)             // 'a.b.b'
 * let fn3 = fn('c', 'd')
 * console.log(fn3.key)             // 'a.b.c.d'
 * ------
 * @param keys
 * @returns
 */
export function curryKey(...keys: Array<string | number>): CurryKey {
    const genFn = (key: string | number, ...ks: Array<string | number>) =>
        getKeyFn(genFn, [key, ...ks].filter((item) => item !== '' && item !== undefined).join('.'))
    const getKeyFn = (fn: typeof genFn, key: string | number) =>
        Object.assign((...kvs: Array<string | number>) => fn(key, ...kvs), {
            key: `${key}`,
            getChildKey: (...childKeys: Array<string | number>) => {
                return [key, ...childKeys].filter((item) => item !== '' && item !== undefined).join('.')
            }
        })
    return getKeyFn(genFn, keys.join('.'))
}

/**
 * 根据路径来获取值
 * @param data 待获取的对象
 * @param key 需要获取的key路径
 * @returns 返回获取到的值
 */
function getObjectValue(data: any = {}, key: string): any {
    const keys = key.split('.')
    if (keys.length === 0) {
        return
    }
    let ret = data
    for (let i = 0; i < keys.length; i++) {
        const key = keys[i]
        if (!ret) {
            return
        }
        ret = ret[key]
    }
    return ret
}

/**
 * 防抖函数
 * @param func 调用函数
 * @param wait 等待时间
 * @param immediate 是否立即执行
 * @returns
 */
function debounce(func: Function, wait: number, immediate?: boolean) {
    let timeout: number | null, result

    const debounced: {
        (): void
        cancel?: () => any
    } = function (this: any, ...args) {
        let context = this

        if (timeout) clearTimeout(timeout)
        if (immediate) {
            // 如果已经执行过，不再执行
            let callNow = !timeout
            timeout = setTimeout(function () {
                timeout = null
            }, wait)
            if (callNow) result = func.apply(context, args)
        } else {
            timeout = setTimeout(function () {
                func.apply(context, args)
            }, wait)
        }
        return result
    }

    debounced.cancel = function () {
        if (timeout !== null) {
            clearTimeout(timeout)
        }
        timeout = null
    }

    return debounced
}

/**
 * 替换变量文本
 * @param text 替换文本
 * @param kvs 替换的map，key为变量，value为替换后的值
 * @param interpolate 变量分隔符
 * @return 替换后的文本
 */
function compileTemplate(text = '', kvs: Record<string, any>, interpolate = /{([\s\S]+?)}/g) {
    return text.replace(interpolate, (match, p1) => {
        if (kvs.hasOwnProperty(p1)) {
            return kvs[p1]
        }
        return match
    })
}

function throttle(func: () => void, delay: number) {
    let prev = Date.now()
    return () => {
        const now = Date.now()
        if (now - prev >= delay) {
            func()
            prev = Date.now()
        }
    }
}

function objectClone(value) {
    return cloneDeep(value)
}

function isDef(val) {
    return val !== undefined && val !== null
}

function isNull(val){
    return !isDef(val) || val === ''
}

function isFunction(val) {
    return typeof val === 'function'
}

function isObject(val) {
    return val !== null && typeof val === 'object'
}

function isArray(val){
    return Array.isArray(val)
}

function isPromise(val) {
    return isObject(val) && isFunction(val.then) && isFunction(val.catch)
}

function toObject(val) {
    if(isObject(val)){
        return val
    }

    if(typeof val === 'string'){
        if(/^\{.*\}$/.test(val)){
            return JSON.parse(val)
        }
    }

    return {}
}

function toArray(val, allowEmpty=false) {
    if (isArray(val)) {
        return val
    }

    if(typeof val === 'string'){
        if(/^\[.*\]$/.test(val)){
            return JSON.parse(val)
        }
    }

    if(isNull(val)){
        return allowEmpty ? [val] : []
    }

    return [val]
}

function formatNumber(value, min = 1, max = Number.MAX_VALUE, allowEmpty = false) {
    if (allowEmpty && isNull(value)) {
        return value
    }
    value = isNull(value) ? 0 : +value
    value = Math.max(Math.min(max as number, value), min as number)
    return value
}

function isImageUrl(url) {
    let IMAGE_REGEXP = /\.(jpeg|jpg|gif|png|svg|webp|jfif|bmp|dpg)/i
    return IMAGE_REGEXP.test(url)
}

function isVideoUrl(url) {
    let VIDEO_REGEXP = /(\.(swf|avi|flv|mpg|rm|mov|wav|asf|3gp|mkv|rmvb|mp4))$/i
    return VIDEO_REGEXP.test(url)
}

// 判断是否为base64
function isBase64(text) {
    return /^\s*data:(?:[a-z]+\/[a-z0-9-+.]+(?:;[a-z-]+=[a-z0-9-]+)?)?(?:;base64)?,([a-z0-9!$&',()*+;=\-._~:@/?%\s]*?)\s*$/i.test(
        text
    )
}

// base64 转 blobURL
function base64ToBlobURL(base64) {
    let arr = base64.split(','),
        mime = arr[0].match(/:(.*?);/)[1],
        bstr = atob(arr[1]),
        n = bstr.length,
        u8arr = new Uint8Array(n)
    while (n--) {
        u8arr[n] = bstr.charCodeAt(n)
    }
    let blob = new Blob([u8arr], { type: mime })
    // blob 转 blobURL
    let windowURL = window.URL || window.webkitURL
    let blobURL = windowURL.createObjectURL(blob)
    return blobURL
}

// 将base64转换为文件
function base64ToFile(dataurl, fileName) {
    let arr = dataurl.split(',')
    let imgType = arr[0].match(/:(.*?);/)[1]
    let bstr = atob(arr[1])
    let n = bstr.length
    let u8arr = new Uint8Array(n)
    while (n--) {
        u8arr[n] = bstr.charCodeAt(n)
    }
    return new File([u8arr], fileName, { type: imgType })
}

function fileToBase64(file) {
    // 創建 FileReader 解析爲 base64 格式
    let fileReader = new FileReader()
    fileReader.readAsDataURL(file)
    return new Promise((resolve, reject) => {
        // 解析成功
        fileReader.onload = function () {
            resolve(fileReader.result)
        }
        // 發生錯誤
        fileReader.onerror = function (err) {
            console.error('error: 文件讀取發生錯誤', JSON.stringify(err))
            reject(err)
        }
    })
}
function buildQueryString(params: Record<string, any>) {
    // 对get请求的参数(key,value)都要进行编码 系统管理和操作日志都需要用到（若依框架）
    let queryString = '';
    for (let key in params) {
      if (params.hasOwnProperty(key) && params[key]) {
        let ontherKey = encodeURIComponent(key)
        let value = encodeURIComponent(params[key]); // 对参数值进行编码  
        if (queryString) {
          queryString += '&'; // 如果已经存在其他参数，则添加'&'分隔符  
        }
        queryString += `${ontherKey}=${value}`; // 拼接键和编码后的值  
      }
    }
    return queryString;
  }

export function useUtils() {
    return {
        loadUrlQuery,
        getUrlQuery,
        copyText,
        deepObjectMerge,
        compareVersion,
        QRCode,
        md5,
        getAssetsUrl,
        curryKey,
        getObjectValue,
        debounce,
        compileTemplate,
        throttle,
        objectClone,
        isDef,
        isNull,
        isFunction,
        isObject,
        isArray,
        toObject,
        toArray,
        formatNumber,
        isImageUrl,
        isVideoUrl,
        isPromise,
        isBase64,
        base64ToBlobURL,
        base64ToFile,
        fileToBase64,
        buildQueryString,
        utils
    }
}
