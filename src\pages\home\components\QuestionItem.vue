<template>
  <div class="question">
    <div class="q" @click="isHide = !isHide">
      <em>Q</em>
      <p>{{ question }}</p>
      <div class="flag">
        <span>{{!isHide ? $lang?.首页['收起'] : $lang?.首页['展开']}}</span>
        <img :class="{ hide: isHide }" :src="$imgs['mobile/up.png']" />
      </div>
      <div class="triangle"></div>
    </div>
    <div class="a" :class="{ hide: isHide }">
      <p v-html="answer"></p>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'

interface Props {
  question: string
  answer: string
}

defineProps<Props>()

const isHide = ref(false)
</script>
<style scoped lang="less">
.question {
  margin-bottom: 24px;
  .q {
    width: 100%;
    background: #00a7ff url('@/assets/imgs/pc/icon.png') no-repeat 96% top;
    background-size: contain;
    display: flex;
    align-items: center;
    padding: 30px;
    border-radius: 32px 32px 0 0;
    position: relative;
    .flag {
      display: flex;
      align-items: center;
      margin-left: 20px;
      span {
        display: block;
        font-size: 24px;
        color: #f5f5f5;
      }
      img {
        width: 48px;
        height: 48px;
        transition: all 0.3s;
        &.hide{
          transform: rotate(180deg);
        }
      }
    }
    .triangle {
      width: 0;
      height: 0;
      border-left: 20px solid transparent;
      border-right: 20px solid transparent;
      border-top: 20px solid #00a7ff;
      position: absolute;
      left: 0;
      right: 0;
      margin: auto;
      bottom: -10px;
    }
    em {
      display: block;
      font-style: normal;
      width: 48px;
      height: 48px;
      line-height: 48px;
      background: #ffffff;
      border-radius: 68px;
      font-size: 40px;
      color: #00a7ff;
      text-align: center;
    }
    p {
      flex: 1;
      padding-left: 24px;
      font-size: 28px;
      color: #ffffff;
    }
  }
  .a {
    background: white;
    font-size: 28px;
    color: rgba(38, 39, 41, 0.8);
    line-height: 42px;
    border-radius: 0px 0px 32px 32px;
    height: auto;
    overflow: hidden;
    p{
      padding: 48px 32px;
    }
    &.hide{
      height: 0;
    }
  }
}
</style>
