import { useLang, useUtils } from '@/hook'
import { useAppStore } from '@/store'
import { createRouter, createWebHashHistory, type RouteRecordRaw } from 'vue-router'
import { getVersionForAosAndIos, showHeaderRight, inApp, configureShare } from '@via/mylink-sdk'

const { utils } = useUtils()
const { lang, setLang, state } = useLang()

// 如果是通配符，需要替换成默认语言tc，否则会报错
if (utils.getHashQueryString('lang') == '<<cmcchkhsh_cmplang>>') {
  utils.updateQuery(
    {
      lang: 'tc'
    },
    false
  )
  setLang('tc')
}

const routes: RouteRecordRaw[] = [
  {
    path: '/',
    redirect: 'home'
  },
  {
    path: '/home',
    name: 'Home',
    component: () => import('@/pages/home/<USER>'),
    meta: {
      title: {
        tc: 's/ash年青人專區',
        sc: 's/ash年青人专区',
        en: 's/ash Youth Zone'
      },
      jumpRoute: 'Webhome',
      isPc: false
    }
  },
  {
    path: '/webhome',
    name: 'Webhome',
    component: () => import('@/pages/webHome/webHome.vue'),
    meta: {
      title: {
        tc: 's/ash年青人專區',
        sc: 's/ash年青人专区',
        en: 's/ash Youth Zone'
      },
      jumpRoute: 'Home',
      isPc: true
    }
  },
  {
    path: '/:pathMatch(.*)*',
    redirect: 'home'
  }
]

const router = createRouter({
  history: createWebHashHistory(),
  routes,
  scrollBehavior(to, from, savedPosition) {
    // console.log('======>', to)
    // console.log('======>', from)
    // console.log('======>', savedPosition)
  }
})

let luck = false
router.beforeEach(async (to, from, next) => {
  const appStore = useAppStore()
  if (to.meta.title) {
    if (typeof to.meta.title === 'object') {
      document.title = (to.meta.title as Record<string, any>)[lang.value]
    } else {
      document.title = to.meta.title as string
    }
  }

  let routerName = utils.getHashQueryString('routerName', window.parent.location.href)
  if (routerName && !luck) {
    luck = true
    next({ name: routerName })
    return
  }

  const device = navigator.userAgent.toLowerCase()
  if (/ipad|iphone|ios|midp|rv:*******|ucweb|HarmonyOS|android|windows ce|windows mobile/.test(device) || appStore.isMiniprogram) {
    //移动端
    appStore.changeIsPC(false)
    if (to.meta.isPc) {
      // 给body添加样式
      next({
        name: to.meta.jumpRoute as string
      })
      return
    }
  } else {
    document.body.classList.add('pc')
    appStore.changeIsPC(true)
    if (!to.meta.isPc) {
      // 给body添加样式
      next({
        name: to.meta.jumpRoute as string
      })
      return
    }
    //pc端
  }
  next()
})

router.afterEach(async (to, from) => {
  const appStore = useAppStore()
  if (!appStore.isDecEnv) {
    if (inApp.value) {
      const version = await getVersionForAosAndIos()
      if (version < 1060) {
        const urlObj = new URL(location.href.split('#')[0])
        const shareUrl = urlObj.toString()
        configureShare({
          url: shareUrl,
          title: state?.分享?.title,
          content: state?.分享?.desc
        })
      } else {
        if (to.name == 'Home') {
          let url = location.origin + location.pathname
          if (lang.value === 'sc') {
            url = url.replace('/sc/', '/tc/')
          } else if (lang.value === 'en') {
            url = url.replace('/en/', '/tc/')
          }
          console.log('一键返回配置==================>', url + '#/home', '语言==========》', lang.value)

          showHeaderRight(url + '#/home')
          // showHeaderRight('https://omniapi-uat.hk.chinamobile.com/viaActivity/external/tc/slash-youth-zone/index.html#/home')
        } else if (to.name == 'Main') {
          let url = location.origin + location.pathname
          if (lang.value === 'sc') {
            url = url.replace('/sc/', '/tc/')
          } else if (lang.value === 'en') {
            url = url.replace('/en/', '/tc/')
          }
          console.log('一键返回配置==================>', url + '#/main', '语言==========》', lang.value)
          showHeaderRight(url + '#/main')
        } else {
          showHeaderRight('cmcchkhsh://home')
        }
      }
    }
  }
})

export default router
