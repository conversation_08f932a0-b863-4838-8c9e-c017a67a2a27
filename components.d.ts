/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    Base: typeof import('./src/components/Base.vue')['default']
    Chat: typeof import('./src/components/chat/index.vue')['default']
    Loading: typeof import('./src/components/Loading.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    Toast: typeof import('./src/components/Toast/toast.vue')['default']
    VanSticky: typeof import('vant/es')['Sticky']
  }
}
