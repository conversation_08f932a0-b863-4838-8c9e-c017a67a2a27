import { watch } from 'vue'
import { closeWebView, getSystem, getUserInfo, inApp, openApp } from '@via/mylink-sdk'
import { useEnvConfig, useLang, usePageVisibility, useUtils, useJsonConfig, useGTM } from '@/hook'
import type { AppStoreType} from './type'
import xhr, { setJwt, setToken } from '@/services/xhr/index'
import wx from 'weixin-js-sdk'
import { activeDialog, getDialog } from '@/components/dialogs/index'
const { sendGTMData } = useGTM()
const { utils, copyText } = useUtils()
const { jsonConfig } = useJsonConfig()
import router from '@/router'

function updateToken(url: string, newToken: string) {
  if (newToken === '') {
    // 删除 cmhkToken 参数及其值
    return url.replace(/([&?])cmhkToken=[^&]*(&|$)/, (match, p1, p2) => {
      // 如果 token 是第一个参数，保留问号
      return p1 === '?' ? (p2 ? '?' : '') : p2 ? p1 : ''
    })
  } else if (url.includes('cmhkToken')) {
    // 替换 cmhkToken 的值
    return url.replace(/(cmhkToken=)[^&]*/, `$1${newToken}`)
  } else {
    // 添加 cmhkToken 参数
    const separator = url.includes('?') ? '&' : '?'
    return `${url}${separator}cmhkToken=${newToken}`
  }
}

/**
 * 手动执行唤起登录方法
 */
export async function handleTryLogin(this: AppStoreType) {
  const env = useEnvConfig()
  const { lang } = useLang()
  const visibility = usePageVisibility()
  if (this.isDecEnv || this.isMiniprogram || this.channel != '1007') {
    let routerName = router.currentRoute.value.name as string
    console.log('this.loginUrl===========>', this.loginUrl, window.$wujie?.props, routerName)
    function addOrReplaceRouterName(url: string, routerNameValue: string) {
      // 定义正则表达式来匹配routerName参数
      const regex = /(\?|&)(routerName=[^&]*)/

      // 检查URL中是否已经存在routerName参数
      if (regex.test(url)) {
        // 替换现有的routerName参数
        url = url.replace(regex, `$1routerName=${encodeURIComponent(routerNameValue)}`)
      } else {
        // 添加routerName参数
        const separator = url.includes('?') ? '&' : '?'
        url += `${separator}routerName=${encodeURIComponent(routerNameValue)}`
      }

      return url
    }
    let loginUrl = ''
    if (this.loginUrl) {
      if (!this.loginUrl.includes('routerName')) {
        loginUrl = decodeURIComponent(this.loginUrl) + `?routerName=${routerName}`
      } else {
        loginUrl = addOrReplaceRouterName(decodeURIComponent(this.loginUrl), routerName)
      }
    }
    switch (env.RUN_ENV) {
      case 'beta':
        window.parent.location.href = this.loginUrl
          ? loginUrl
          : `https://omniapi.hk.chinamobile.com/${lang.value}/home/<USER>
              location.origin + location.pathname + `?routerName=${routerName}`
            )}`
        break
      case 'production':
        window.parent.location.href = this.loginUrl
          ? loginUrl
          : `https://omniapi.hk.chinamobile.com/${lang.value}/home/<USER>
              location.origin + location.pathname + `?routerName=${routerName}`
            )}`
        break
      default:
        window.parent.location.href = this.loginUrl
          ? loginUrl
          : `https://omniapi-uat.hk.chinamobile.com/${lang.value}/home/<USER>
              location.origin + location.pathname + `?routerName=${routerName}&debug=true`
            )}`
        break
    }
    // switch (env.RUN_ENV) {
    //   case 'beta':
    //     window.parent.location.href = this.loginUrl ? (this.loginUrl.indexOf('https') == -1 ? `https://${this.loginUrl}?redirect=${encodeURIComponent(window.parent.location.origin + window.parent.location.pathname)}` : this.loginUrl + `?redirect=${encodeURIComponent(window.parent.location.origin + window.parent.location.pathname)}`) : `https://omniapi.hk.chinamobile.com/${lang.value}/home/<USER>
    //     break
    //   case 'production':
    //     window.parent.location.href = this.loginUrl ? (this.loginUrl.indexOf('https') == -1 ? `https://${this.loginUrl}?redirect=${encodeURIComponent(window.parent.location.origin + window.parent.location.pathname)}` : this.loginUrl + `?redirect=${encodeURIComponent(window.parent.location.origin + window.parent.location.pathname)}`) : `https://omniapi.hk.chinamobile.com/${lang.value}/home/<USER>
    //     break
    //   default:
    //     window.parent.location.href = this.loginUrl ? (this.loginUrl.indexOf('https') == -1 ? `https://${this.loginUrl}?redirect=${encodeURIComponent(window.parent.location.origin + window.parent.location.pathname)}` : this.loginUrl + `?redirect=${encodeURIComponent(window.parent.location.origin + window.parent.location.pathname)}`) : `https://omniapi-uat.hk.chinamobile.com/${lang.value}/home/<USER>
    //     break
    // }
  } else {
    let shouldClose = false
    if (inApp.value) {
      watch([visibility, () => this.isPullLoginPage], () => {
        if (!visibility.isHidden) {
          const replaceUrl = (link: string) => {
            const url = new URL(link)
            const params = new URLSearchParams(url.search)
            params.set('cmhkToken', '<<cmcchkhsh_ssoToken>>')
            url.search = params.toString()
            return url.toString()
          }
          if (!this.isLogin && this.isPullLoginPage) {
            if (getSystem() == 'ios') {
              if (shouldClose) {
                closeWebView()
                return
              }
              shouldClose = true
              setTimeout(() => {
                location.href = `openurl-modal://${replaceUrl(location.href)}`
              }, 1000)
            } else {
              window.location.replace(replaceUrl(location.href))
              closeWebView()
            }
          }
        }
      })
      if (!this.isLogin) {
        const userInfo = await getUserInfo(true)
        if (userInfo) {
          this.isPullLoginPage = true
        }
      }
    } else {
      const link = updateToken(location.href, '<<cmcchkhsh_ssoToken>>')
      openApp(link)
    }
  }
}

export function getWxEnv(this: AppStoreType) {
  wx.miniProgram.getEnv((res) => {
    if (res.miniprogram) {
      this.isMiniprogram = true
    }
  })
}

// 是否是pc端
export function changeIsPC(this: AppStoreType, bol: boolean) {
  this.isPC = bol
}

//获取活动状态
export async function getActiveState(this: AppStoreType) {
  this.activityState = await xhr.omniapiPost('/activity/state')
}

//登录
export async function login(this: AppStoreType) {
  const env = useEnvConfig()
  let token = window?.$wujie?.props?.data?.cmhkToken || sessionStorage.getItem('cmhkToken') || utils.getHashQueryString('cmhkToken') || ''

  // if (env.RUN_ENV === 'develop') {
  //   token = await getToken()
  // }
  console.log('token========>', token, '无界=============》', window?.$wujie?.props?.data)
  // 登录验证获取鉴权
  if (token || this.channel != '1007') {
    if (token === '<<cmcchkhsh_ssoToken>>') {
      return
    }
    await xhr
      .omniapiPost('/user/login', { data: { token }, headers: { cmhkToken: token }, hideCommToast: true, timeout: 15000 })
      .then((res) => {
        if (res.jwt) {
          setJwt(res.jwt)
          this.loginPhone = res.showPho
          this.isLogin = true
        }
      })
      .catch((err) => {
        // if (env.RUN_ENV === 'develop') {
        //   this.loginPhone = '51919796'
        //   setJwt(
        //     'eyJhbGciOiJIUzUxMiJ9.eyJkYXRhIjoiM2U5NTVlOGI5NDc0NDMyOWE1NzViZmYwNjUyNTk2NzAiLCJleHAiOjE3NDM5NjM3NDEsInN1YiI6IiJ9.wZ2vuXBmjbAtTFVGfpYR8JuPFfkMSLIqygdNsDPrwEDF-khtJFWhYHl_iGUda9LCJmYUjLac94emi0TLmIKTbA'
        //   )
        //   useEggStore().eggList = []
        //   this.isLogin = true
        // }
        console.log(err)
      })
  }
}

/**
 * 发送短信
 * @param couponCode 卡券code
 * @param phone 手机号码
 * @returns
 */
export async function sendSMS(couponCode: string, phone: string) {
  try {
    if (phone.length !== 8 && phone.length !== 11) {
      return
    }
    let res = await xhr.omniapiPost<boolean>('/coupon/sms', { data: { couponCode, phone } })
    return res
  } catch (error) {
    console.log(error)
  }
}

export async function getToken() {
  try {
    let res = await xhr.omniapiPost('/test/omni/getSSOToken', {
      data: {
        // msisdn: '40000004'
        msisdn: '92046424' //后付
      },
      timeout: 10000
    })
    return res.result.token
  } catch (error) {
    console.log(error)
  }
}

/**
 * 根据屏幕大小判断是否是移动设备
 */
export function checkIsMobild(this: AppStoreType) {
  const changeSize = () => {
    this.isMobile = window.innerWidth < 750
  }
  changeSize()
  window.addEventListener('resize', () => {
    changeSize()
  })
}

/**
 * 分享方法集合
 * @param channel 分享的渠道
 * @param title 标题
 * @param desc 描述
 * @param link 链接
 */
export function share(
  this: AppStoreType,
  channel: 'wechat' | 'whatsApp' | 'facebook' | 'redNote' | 'dy' | 'copy',
  title: string,
  desc: string,
  link: string
) {
  const { lang, state } = useLang()
  const env = useEnvConfig()
  // 清除链接上的token
  link = updateToken(link, '')
  // 如果是mylink渠道，加上token占位符
  // if(this.channel == '1007') {
  //   link = updateToken(link, '<<cmcchkhsh_ssoToken>>')
  // }
  switch (channel) {
    case 'facebook':
      if (this.isDecEnv) {
        // TODO 构建Facebook分享URL,用到一个中间页，要改标题描述请在public/share-xx.html中修改
        // const shareLink = `${env.OMNI_BASE}/viaActivity/external/${lang.value}/slash-youth-zone/share-${lang.value}.html` + '?redirect=' + encodeURIComponent(link)
        const shareLink = link
        const fbURL = 'https://www.facebook.com/sharer/sharer.php?u=' + encodeURIComponent(shareLink)
        // 打开新的窗口或标签页
        window.open(fbURL, 'facebook-share-dialog', 'width=626,height=436')
      } else {
        if (inApp.value) {
          let location: any = `cmcchkhsh://openshare?title=${title}&content=${desc}&url=${encodeURIComponent(
            link
          )}&img=${encodeURIComponent('https://cdn.mylinkapp.hk/via/atv/common/share.png')}&type=facebook`
          window.location = location
        } else {
          // TODO 构建Facebook分享URL,用到一个中间页，要改标题描述请在public/share-xx.html中修改
          // const shareLink = `${env.OMNI_BASE}/viaActivity/external/${lang.value}/slash-youth-zone/share-${lang.value}.html` + '?redirect=' + encodeURIComponent(link)
          const shareLink = link
          const fbURL = 'https://www.facebook.com/sharer/sharer.php?u=' + encodeURIComponent(shareLink)
          // 打开新的窗口或标签页
          window.open(fbURL, 'facebook-share-dialog', 'width=626,height=436')
        }
      }
      break
    case 'whatsApp':
      if (this.isDecEnv) {
        const text = title + desc + link
        // 构建WhatsApp分享URL
        const waURL = 'https://api.whatsapp.com/send?text=' + encodeURIComponent(text)
        // 打开新的窗口或标签页
        window.open(waURL, '_blank')
      } else {
        if (inApp.value) {
          let location: any = `cmcchkhsh://openshare?title=${title}&content=${desc}&url=${encodeURIComponent(
            link
          )}&img=${encodeURIComponent('https://cdn.mylinkapp.hk/via/atv/common/share.png')}&type=whatsapp`
          window.location = location
        } else {
          const text = title + desc + link
          // 构建WhatsApp分享URL
          const waURL = 'https://api.whatsapp.com/send?text=' + encodeURIComponent(text)
          // 打开新的窗口或标签页
          window.open(waURL, '_blank')
        }
      }
      break
    case 'wechat':
      if (this.isDecEnv) {
        copyText(title + desc + link)
        // this.toastMessage = state?.toast?.鏈接已複製到剪貼簿
      } else {
        if (inApp.value) {
          let location: any = `cmcchkhsh://openshare?title=${title}&content=${desc}&url=${encodeURIComponent(
            link
          )}&img=${encodeURIComponent('https://cdn.mylinkapp.hk/via/atv/common/share.png')}&type=wechat`
          window.location = location
        } else {
          copyText(title + desc + link)
          // this.toastMessage = state?.toast?.鏈接已複製到剪貼簿
        }
      }
      break
    case 'copy':
      copyText(title + desc + link)
      // this.toastMessage = state?.toast?.鏈接已複製到剪貼簿
      break
    default:
      copyText(title + desc + link)
      // this.toastMessage = state?.toast?.鏈接已複製到剪貼簿
  }
}

/**
 * 平滑滚动函数（兼容性更好）
 */
export function smoothScrollTo(targetPosition: number, duration: number = 800) {
  const startPosition = window.pageYOffset || document.documentElement.scrollTop
  const distance = targetPosition - startPosition
  const startTime = performance.now()

  const animateScroll = (currentTime: number) => {
    const timeElapsed = currentTime - startTime
    const progress = Math.min(timeElapsed / duration, 1)

    // 缓动函数
    const easeInOutQuad = (t: number) => t < 0.5 ? 2 * t * t : -1 + (4 - 2 * t) * t
    const easedProgress = easeInOutQuad(progress)

    window.scrollTo(0, startPosition + distance * easedProgress)

    if (progress < 1) {
      requestAnimationFrame(animateScroll)
    }
  }

  requestAnimationFrame(animateScroll)
}

/**
 * 滚动到指定元素 - 适配无界环境
 */
export function scrollToElement(this: AppStoreType, id: string, offset: number = 227) {
  console.log('scrollToElement called with id:', id)
  const element = document.getElementById(id)
  console.log('Found element:', element)

  if (element) {
    // 检查是否在无界环境中
    const isWujie = window.__POWERED_BY_WUJIE__ || window.$wujie
    console.log('Is Wujie environment:', isWujie)

    if (isWujie) {
      // 无界环境：使用 scrollIntoView，让父页面处理滚动
      try {
        console.log('Using scrollIntoView for Wujie environment')
        element.scrollIntoView({
          behavior: 'smooth',
          block: 'start',
          inline: 'nearest'
        })

        // 如果需要偏移调整，可以通过 wujie 的 bus 通信
        if (window.$wujie?.bus?.$emit) {
          window.$wujie.bus.$emit('scroll-with-offset', {
            elementId: id,
            offset: offset
          })
        }

        return
      } catch (e) {
        console.log('Wujie scrollIntoView failed:', e)
      }
    }

    // 非无界环境或无界失败时的处理
    const rect = element.getBoundingClientRect()
    const scrollTop = window.pageYOffset || document.documentElement.scrollTop
    const targetPosition = rect.top + scrollTop - offset

    console.log('Target position:', targetPosition)
    console.log('Current scroll position:', scrollTop)

    // 方法1: 尝试 window.scrollTo
    try {
      window.scrollTo({
        top: targetPosition,
        behavior: 'smooth'
      })
    } catch (e1) {
      console.log('window.scrollTo failed, using custom smooth scroll:', e1)
      // 方法2: 使用自定义平滑滚动
      smoothScrollTo(targetPosition)
    }
  } else {
    console.error('Element not found:', id)
  }
}

/**
 * 滚动到指定的 ref 元素 - 适配无界环境
 */
export function scrollToRef(this: AppStoreType, elementRef: HTMLElement | null, offset: number = 100) {
  console.log('scrollToRef called, element:', elementRef)
  if (elementRef) {
    // 检查是否在无界环境中
    const isWujie = window.__POWERED_BY_WUJIE__ || window.$wujie
    console.log('Is Wujie environment:', isWujie)

    if (isWujie) {
      // 无界环境：直接使用 scrollIntoView
      try {
        console.log('Using scrollIntoView for Wujie environment (ref)')
        elementRef.scrollIntoView({
          behavior: 'smooth',
          block: 'start',
          inline: 'nearest'
        })
        return
      } catch (e) {
        console.log('Wujie scrollIntoView failed for ref:', e)
      }
    }

    // 非无界环境或无界失败时的处理
    try {
      console.log('Trying scrollIntoView for ref...')
      elementRef.scrollIntoView({
        behavior: 'smooth',
        block: 'start',
        inline: 'nearest'
      })

      // 调整偏移
      setTimeout(() => {
        const currentScroll = window.pageYOffset || document.documentElement.scrollTop
        const adjustedPosition = currentScroll - offset
        console.log('Adjusting ref position to:', adjustedPosition)

        window.scrollTo({
          top: adjustedPosition,
          behavior: 'smooth'
        })
      }, 100)

    } catch (e) {
      console.log('scrollIntoView failed for ref, using fallback:', e)
      // 兼容性回退方案
      const rect = elementRef.getBoundingClientRect()
      const scrollTop = window.pageYOffset || document.documentElement.scrollTop
      const targetPosition = rect.top + scrollTop - offset
      smoothScrollTo(targetPosition)
    }
  } else {
    console.error('Element ref is null')
  }
}

/**
 * 初始化滚动设置，避免 CSS 冲突
 */
export function initScrollSettings(this: AppStoreType) {
  // 检查 CSS scroll-behavior 设置
  const htmlStyle = getComputedStyle(document.documentElement)
  const bodyStyle = getComputedStyle(document.body)
  console.log('HTML scroll-behavior:', htmlStyle.scrollBehavior)
  console.log('Body scroll-behavior:', bodyStyle.scrollBehavior)

  // 强制设置 scroll-behavior 为 auto，避免冲突
  document.documentElement.style.scrollBehavior = 'auto'
  document.body.style.scrollBehavior = 'auto'
}
