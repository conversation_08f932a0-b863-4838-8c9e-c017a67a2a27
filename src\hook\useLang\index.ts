import { reactive, Ref, ref, watch, App } from 'vue'
import langAssets, { Lang } from './assets/index'
import Formatter from './format'
import { useUtils } from '../useUtils'

const formatter = new Formatter()

const getLang = () => {
  if(location.href.includes('/tc')) {
    return 'tc'
  } else if(location.href.includes('/en')) {
    return 'en'
  } else if(location.href.includes('/sc')) {
    return 'sc'
  }
  return ''
}
const lang = (getLang() || useUtils().utils.getHashQueryString('lang')) as 'tc' | 'sc' | 'en'

const currentLang = ref<'sc' | 'tc' | 'en'>(lang || window.sessionStorage.getItem('lang') || 'tc')
const outMessage: typeof langAssets = {} as typeof langAssets

type State = Lang & UseLang.Lang

const state: State = reactive<State>(langAssets.tc as State)

const setLang = (locale: 'sc' | 'tc' | 'en') => {
  currentLang.value = locale || 'tc'
  Object.assign(state, langAssets[currentLang.value], outMessage[locale] || {})
  window.sessionStorage.setItem('lang', currentLang.value)
  console.log('当前语言', currentLang.value, state)
}

const setMessages = (messages: Record<'sc' | 'tc' | 'en', UseLang.Lang>) => {
  const keys = Object.keys(messages)
  keys.forEach((key) => {
    outMessage[key] = messages[key]
  })
  Object.assign(state, langAssets[currentLang.value], outMessage[currentLang.value])
}

setLang(currentLang.value)

const onLangChange = (cb: (lang: Ref<'sc' | 'tc' | 'en'>) => void) => {
  watch(
    currentLang,
    () => {
      cb(currentLang)
    },
    { immediate: true }
  )
}

function format(message: string, values: any) {
  return formatter.interpolate(message, values)
}

export function useLang() {
  return {
    state,
    lang: currentLang,
    setLang,
    onLangChange,
    format,
    setMessages
  }
}

export type LangState = State
export type LangValue = typeof currentLang
export type LangFormat = typeof format

export namespace UseLang {
  export interface Lang {}
}

declare module '@vue/runtime-core' {
  interface ComponentCustomProperties {
    $lang: LangState
    $langValue: LangValue
    $langFormat: LangFormat
  }
}
