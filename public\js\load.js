/**
 * 创建资源加载器
 */
const createAssetsLoader = () => {
    /**
     * 用于监听资源加载回调
     * @param scope
     * @param resolve
     * @param reject
     */
    const listenEvent = (scope, resolve, reject) => {
        scope.onload = (e) => {
            scope.onload = null
            resolve(e)
        }
        scope.onerror = (e) => {
            scope.onerror = null
            reject(e)
        }
    }
    /**
     * 加载js
     * @param src
     */
    const _loadJs = (src, module = false, crossorigin, order) => {
        return new Promise((resolve, reject) => {
            let script = document.createElement('script')
            listenEvent(script, resolve, reject)
            script.src = src
            if(module) script.type = 'module'
            if(crossorigin) script.crossOrigin = crossorigin
            Object.keys(order).forEach(key => {
                order[key] && script.setAttribute(key,order[key])
            })
            document.head.appendChild(script)
        })
    }
    /**
     * 加载css
     * @param src
     */
    const _loadCss = (src) => {
        return new Promise((resolve, reject) => {
            let link = document.createElement('link')
            link.rel = 'stylesheet'
            link.type = 'text/css'
            listenEvent(link, resolve, reject)
            link.href = src
            document.head.appendChild(link)
        })
    }

    return {
        _loadJs,
        _loadCss
    }
}

const loader = createAssetsLoader()

async function load(options) {
    let { type, src, module, crossorigin, ...order } = options
    console.log(order)
    const baseUrl = window.__vite_env__.BASE
    if (!src.startsWith('http') && !src.startsWith('//') && baseUrl.startsWith('http') && baseUrl.startsWith('//')) {
        src = (baseUrl || '') + src
        console.log(src)
    }
    let st = new Date().getTime()
    return new Promise((resolve, reject) => {
        console.log(`%c 开始加载${type}`, 'color: #ffffff;background-color: #2a5caa;padding: 5px', src)
        let _loader
        if(type === 'js') _loader = loader['_loadJs'](src, module, crossorigin, order)
        if(type === 'css') _loader = loader['_loadCss'](src)
        if(_loader) {
            _loader.then(() => {
                const et = new Date().getTime()
                console.log(
                `%c ${type}加载完成`,
                'color: #ffffff;background-color: #1d953f;padding: 5px',
                `耗时：${et - st}毫秒 ${src}`
                )
                resolve()
            })
            .catch(reject)
        } else {
            reject()
        }
    })
    .then((res) => {
        return res
    })
    .catch((err) => {
        const et = new Date().getTime()
        console.log(
            `%c ${type}加载失败`,
            'color: #ffffff;background-color: #ff0066;padding: 5px',
            `耗时：${et - st}毫秒 ${src}`
        )
        throw err
    })
}
window.load = load