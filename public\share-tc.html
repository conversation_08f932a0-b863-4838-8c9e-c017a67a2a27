<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>彩蛋Fun享</title>
  <!-- 分享标题 -->
  <meta property="og:title" content="彩蛋Fun享" />
  <!-- 分享描述 -->
  <meta property="og:description" content="彩蛋驚喜．繽fun禮遇．復活節限時優惠等你揀！" />
  <!-- 分享的图片 -->
  <meta property="og:image" content="" />
  <!-- 网站名称 -->
  <meta property="og:site_name" content="彩蛋Fun享" />
  <!-- 内容类型，通常为 article 或 website -->
  <meta property="og:type" content="website" />
  <script>
    function getHashQueryString(name, url) {
      if (url) {
        let Reg = (new RegExp('[?|&]' + name + '=' + '([^&;]+?)(&|#|;|$)').exec(url) || [, ""])
        return decodeURIComponent(Reg[1].replace(/\+/g, '%20')) || null
      } else {
        let Reg = (new RegExp('[?|&]' + name + '=' + '([^&;]+?)(&|#|;|$)').exec(location.href) || [, ""])
        return decodeURIComponent(Reg[1].replace(/\+/g, '%20')) || null
      }
    }
    const redirect = getHashQueryString('redirect')
    location.replace(redirect)
  </script>
</head>

<body>
</body>

</html>