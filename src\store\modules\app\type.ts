import { useAppStore } from '.'

export interface PlanItem {
  'name-sc': string
  'name-tc': string
  'name-en': string
  price: string
  data: string
  month: string
  img: string
  'desc-sc': string[]
  'desc-tc': string[]
  'desc-en': string[]
  url: {
    tc: string
    sc: string
    en: string
  }
}

export interface QuestionItem {
  'question-sc': string
  'question-tc': string
  'question-en': string
  'answer-sc': string
  'answer-tc': string
  'answer-en': string
}

export interface appState {
  isLoading: boolean
  isPC: boolean
  isLogin: boolean
  isPullLoginPage: boolean
  loginPhone: string
  //NOT_START未开始 OPEN活动期 END已结束
  activityState: 'NOT_START' | 'OPEN' | 'END'
  isAfterPayment: boolean
  isMobile: boolean
  isMiniprogram: boolean
  toastMessage: string
  jsonConfig: any
  endTime: string
  // 通用数据
  planList: PlanItem[]
  questionList: QuestionItem[]
}

export type AppStoreType = ReturnType<typeof useAppStore>

export interface urlType {
  link: string
  mylink: string
  patlink: string
  patmylink: string
  patwx: string
  wx: string
}
