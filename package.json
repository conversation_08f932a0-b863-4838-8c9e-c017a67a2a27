{"name": "slash-youth-zone", "description": "slash青年专区", "private": true, "version": "1.0.0", "main": "lib/main.js", "typings": "types/main.d.ts", "files": ["lib", "src", "types"], "scripts": {"sonar": "esno ./scripts/sonarqube-scanner-config.ts", "sonar:dev": "cross-env runEnv=develop yarn sonar", "serve": "cross-env runEnv=develop vite", "build:uat": "cross-env runEnv=uat vite build", "build:beta": "cross-env runEnv=beta vite build", "build:pat": "cross-env runEnv=production vite build", "build": "vue-tsc --noEmit --skipLibCheck && vite build", "test": "vue-tsc --noEmit --skipLibCheck && vite build --mode=test", "build:ts": "tsc -p . && tsc-alias", "preview": "vite preview"}, "dependencies": {"@via/mylink-sdk": "^0.8.8-beta.3", "@vueuse/core": "^8.3.1", "axios": "^0.21.1", "clipboard": "^2.0.11", "core-js": "^3.12.1", "crypto-js": "^4.1.1", "dayjs": "^1.10.7", "jwt-decode": "3.1.2", "lodash": "^4.17.21", "md5js": "^1.0.7", "mitt": "^3.0.0", "pinia": "^2.0.16", "pinia-plugin-persistedstate": "3.1.0", "qs": "^6.10.3", "regenerator-runtime": "^0.14.1", "swiper": "^11.1.15", "vant": "^4.9.1", "vue": "^3.5.12", "vue-router": "^4.2.5", "vue3-canvas-poster": "^1.0.1", "weixin-js-sdk": "^1.6.5"}, "devDependencies": {"@types/crypto-js": "^4.1.1", "@types/file-saver": "^2.0.7", "@types/lodash": "^4.14.202", "@types/node": "^17.0.24", "@types/nprogress": "^0.2.0", "@types/qs": "^6.9.7", "@types/ua-parser-js": "^0.7.36", "@vant/auto-import-resolver": "^1.2.1", "@vitejs/plugin-legacy": "^5.4.3", "@vitejs/plugin-vue": "^4.5.1", "@vitejs/plugin-vue-jsx": "^3.1.0", "@vue/compiler-sfc": "^3.2.22", "babel-plugin-import": "^1.13.3", "cross-env": "^7.0.3", "esno": "^4.8.0", "less": "^4.1.3", "postcss-px-to-viewport-8-plugin": "^1.2.5", "rollup-plugin-external-globals": "^0.6.1", "rollup-plugin-visualizer": "^5.5.2", "sonarqube-scanner": "3.0.1", "typescript": "^4.5.4", "unplugin-auto-import": "^0.17.6", "unplugin-vue-components": "^0.27.0", "vite": "4.5.0", "vite-plugin-compression": "^0.3.5", "vite-plugin-html": "^3.2.0", "vue-tsc": "^1.8.24"}}