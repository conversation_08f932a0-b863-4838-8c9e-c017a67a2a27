import { getElementTop, getScrollTop, ScrollElement, setScrollTop } from './dom'
import { useScrollParent } from './useScrollParent'

export const inBrowser = typeof window !== 'undefined'

export function raf(fn: FrameRequestCallback): number {
    return inBrowser ? requestAnimationFrame(fn) : -1
}

export function scrollTopTo(scroller: ScrollElement, to: number, duration: number, callback: () => void) {
    let current = getScrollTop(scroller)

    const isDown = current < to
    const frames = duration === 0 ? 1 : Math.round((duration * 1000) / 16)
    const step = (to - current) / frames

    function animate() {
        current += step

        if ((isDown && current > to) || (!isDown && current < to)) {
            current = to
        }

        setScrollTop(scroller, current)

        if ((isDown && current < to) || (!isDown && current > to)) {
            raf(animate)
        } else if (callback) {
            raf(callback as FrameRequestCallback)
        }
    }

    animate()
}

let timer = -1
export function scrollToTop() {
    let currentNum = document.body.scrollTop || document.documentElement.scrollTop
    let t = 0
    clearInterval(timer)
    let dir = 1
    if (0 > currentNum) {
        dir = -1
    }
    timer = setInterval(() => {
        // 匀加速运动
        t++
        currentNum -= 2 * t * dir
        document.body.scrollTop = document.documentElement.scrollTop = currentNum
        if ((dir === 1 && currentNum <= 0) || (dir === -1 && currentNum >= 0)) {
            document.body.scrollTop = document.documentElement.scrollTop = 0
            clearInterval(timer)
        }
    }, 16.7)
}

export function scrollElementToTop(id: string) {
    const ele = document.querySelector(id) as HTMLElement
    if (ele) {
        const scroller = useScrollParent(ele)
        if (!scroller) {
            return
        }
        const to = getElementTop(ele, scroller)
        scrollTopTo(scroller, to, 0.3, () => {})
    }
}
