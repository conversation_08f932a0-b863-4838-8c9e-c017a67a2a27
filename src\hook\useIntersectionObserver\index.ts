import { ref, onMounted, onBeforeUnmount } from 'vue'
import type { Ref } from 'vue'

export function useIntersectionObserver(itemRefs: Ref<HTMLElement[]>, tabMenuIndex: Ref<number>) {
  const observer = ref<IntersectionObserver | null>(null)

  onMounted(() => {
    observer.value = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            const index = itemRefs.value.findIndex((item) => item === entry.target)
            if (index !== -1) {
              tabMenuIndex.value = index
            }
          }
        })
      },
      {
        threshold: 0.5
      }
    )

    itemRefs.value.forEach((item) => {
      if (item) observer.value?.observe(item)
    })
  })

  onBeforeUnmount(() => {
    if (observer.value) {
      observer.value.disconnect()
    }
  })

  return {
    observer
  }
}
