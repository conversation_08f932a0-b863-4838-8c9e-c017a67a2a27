import axios  from 'axios'
import { useUtils } from '@/hook'
import { useEnvConfig } from '@/hook'
import type { ReturnData } from './index'
import { AxiosResponse } from 'axios'
const { utils } = useUtils()
const instance = axios.create({
    baseURL: useEnvConfig().API_HOST,
    // 超时时间
    timeout: 60000,
    headers: {
        'X-Requested-With': 'XMLHttpRequest'
    }
})
// instance.defaults.headers = {'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8'};

// 请求拦截
instance.interceptors.request.use((config)=>{
    // let token = utils.getHashQueryString('token')
    // if(token){
    //     config.headers['Authorization'] = 'Bear ' + token
    // }
    return config
},error => Promise.reject(error))

//错误处理
// 响应拦截（配置请求回来的信息）
instance.interceptors.response.use(function (response: AxiosResponse<ReturnData>) {
    let data = response.data;

    return response;
}, utils.catchError);
export default instance