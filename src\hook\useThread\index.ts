class Thread {
    fn: Function
    constructor(fn: Function) {
      this.fn = fn
    }
  
    async run() {
      return this.fn()
    }
  }
  
  class ThreadPool {
    size: number
    threadList: Array<{ thread: Thread; resolve: Function; reject: Function }> = []
    runNum = 0
    isRunning = false
  
    constructor(size: number) {
      this.size = size
    }
  
    add(thread: Thread) {
      return new Promise((resolve, reject) => {
        this.threadList.push({ thread, resolve, reject })
        if (!this.isRunning) {
          this.run()
        }
      })
    }
  
    run() {
      this.isRunning = true
      if (this.threadList.length === 0 && this.runNum === 0) {
        this.isRunning = false
        return
      }
      while (this.threadList.length > 0 && this.runNum < this.size) {
        this.runNum++
        const { thread, resolve, reject } = this.threadList.shift() as {
          thread: Thread
          resolve: Function
          reject: Function
        }
        thread
          .run()
          .then((res) => {
            this.runNum--
            this.run()
            resolve(res)
          })
          .catch((err) => {
            this.runNum--
            this.run()
            reject(err)
          })
      }
    }
  }
  
  export function useThread() {
    return { Thread, ThreadPool }
  }
  