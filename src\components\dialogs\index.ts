import { useDialog, DialogOpt } from '@/hook'
import wapDialogs from './wap'
import webDialogs from './web'
import EasterEgg from './EasterEgg.vue'
import OnStageVoucher from './OnStageVoucher.vue'
import GetCouponsSuccess from './GetCouponsSuccess.vue'
import OrdinaryVoucher from './OrdinaryVoucher.vue'
import TextDialog from './TextDialog.vue'
import LongContent from './LongContent.vue'
import StoredValueCard from './StoredValueCard.vue'

import phoneLogin from './phoneLogin.vue'
import rule from './rule.vue'
import type { Component } from 'vue'

const instances = {
  EasterEgg, // 社交彩蛋 弹窗
  TextDialog, // 社交任务，点击去完成后的消息提示弹窗
  OnStageVoucher, // 上台券彩蛋 弹窗
  GetCouponsSuccess, // 领券成功弹窗
  OrdinaryVoucher, // 家宽or手机券 彩蛋 弹窗
  StoredValueCard, // 储值卡弹窗
  phoneLogin,
  ...webDialogs,
  ...wapDialogs,
  rule,
  LongContent
}
type Instances = keyof typeof instances

export let dialog: ReturnType<typeof useDialog> = {} as ReturnType<typeof useDialog>
export let activeDialog: ReturnType<typeof dialog.get> = {
  close: async () => false
} as ReturnType<typeof dialog.get>

const registerDialogs = (
  instances: Record<
    string,
    | Component
    | {
        componentName?: string
        component: Component
        opts?: DialogOpt
      }
  >
) => {
  dialog = useDialog(instances)
}
registerDialogs(instances)
export const showDialog = (name: Instances, props?: Record<string, any>, opts?: DialogOpt) => {
  activeDialog = dialog.get(name, opts)
  return activeDialog.show(props, opts)
}
export const getDialog = (name: Instances, opts?: DialogOpt) => {
  activeDialog = dialog.get(name, opts)
  return activeDialog
}
