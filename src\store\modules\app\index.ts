import { defineStore } from 'pinia'
import * as actions from './actions'
import { useUtils, useLang } from '@/hook'
import type { appState } from './type'

export const useAppStore = defineStore('appStore', {
  state: (): appState => ({
    isLoading: false,
    isPC: false, // 是否是PC端
    isMobile: true,
    isLogin: false,
    loginPhone: '', // 已登录的手机号
    isPullLoginPage: false,
    // NOT_START未开始 OPEN活动期 END已结束
    activityState: 'NOT_START',
    isAfterPayment: false,
    isMiniprogram: false,
    toastMessage: '',
    jsonConfig: [],
    endTime: '',
    planList: [
      {
        'name-sc': '【学生体验卡】s/ash 5G服务计划 30GB',
        'name-tc': '【學生體驗卡】s/ash 5G服務計劃 30GB',
        'name-en': '【Student Trial Offer】s/ash 5G Service Plan 30GB',
        price: '118',
        data: '30GB',
        month: '12',
        img: '30-',
        'desc-sc': [
          '「首账单月无条件取消服务合约」',
          '免首月及第5个月月费',
          '送4GB中国内地及澳门数据',
          '送任用社交及娱乐数据组合',
          '豁免$216行政费'
        ],
        'desc-tc': [
          '「首賬單月無條件取消服務合約」 ',
          '免首月及第5個月月費',
          '送4GB中國內地及澳門數據',
          '送任用社交及娛樂數據組合',
          '豁免$216行政費'
        ],
        'desc-en': [
          'Unconditional service contract cancellation within the 1st billing month ',
          'FREE 1st & 5th Monthly Fees',
          'FREE 4GB Mainland China and Macau Data',
          'Free Unlimited Social & Entertainment Data Pack (Value: $38/month)',
          'Waiver of $216 administration fee'
        ],
        url: {
          tc: 'https://www.hk.chinamobile.com/tc/home/<USER>/detail?commodityId=21202506261938167395267514368',
          sc: 'https://www.hk.chinamobile.com/sc/home/<USER>/detail?commodityId=21202506261938167395267514368',
          en: 'https://www.hk.chinamobile.com/en/home/<USER>/detail?commodityId=21202506261938167395267514368'
        }
      },
      {
        'name-sc': '【学生体验卡】s/ash 5G服务计划 50GB',
        'name-tc': '【學生體驗卡】s/ash 5G服務計劃 50GB',
        'name-en': '【Student Trial Offer】s/ash 5G Service Plan 50GB',
        price: '158',
        data: '50GB+10GB',
        month: '12',
        img: '60-',
        'desc-sc': [
          '「首账单月无条件取消服务合约」',
          '免首月及第5个月月费',
          '送5GB中国内地及澳门数据',
          '送任用社交及娱乐数据组合',
          '豁免$216行政费'
        ],
        'desc-tc': [
          '「首賬單月無條件取消服務合約」 ',
          '免首月及第5個月月費',
          '送5GB中國內地及澳門數據',
          '送任用社交及娛樂數據組合',
          '豁免$216行政費'
        ],
        'desc-en': [
          'Unconditional service contract cancellation within the 1st billing month ',
          'FREE 1st & 5th Monthly Fees',
          'FREE 5GB Mainland China and Macau Data',
          'Free Unlimited Social & Entertainment Data Pack (Value: $38/month)',
          'Waiver of $216 administration fee'
        ],
        url: {
          tc: 'https://www.hk.chinamobile.com/tc/home/<USER>/detail?commodityId=21202506261938168137793540096',
          sc: 'https://www.hk.chinamobile.com/sc/home/<USER>/detail?commodityId=21202506261938168137793540096',
          en: 'https://www.hk.chinamobile.com/en/home/<USER>/detail?commodityId=21202506261938168137793540096'
        }
      },
      {
        'name-sc': '【学生体验卡】s/ash 5G服务计划 100GB',
        'name-tc': '【學生體驗卡】s/ash 5G服務計劃 100GB',
        'name-en': '【Student Trial Offer】s/ash 5G Service Plan 100GB',
        price: '198',
        data: '100GB',
        month: '12',
        img: '100-',
        'desc-sc': [
          '「首账单月无条件取消服务合约」',
          '免首月及第5个月月费',
          '送7GB中国内地及澳门数据',
          '送任用社交及娱乐数据组合',
          '豁免$216行政费'
        ],
        'desc-tc': [
          '「首賬單月無條件取消服務合約」 ',
          '免首月及第5個月月費',
          '送7GB中國內地及澳門數據',
          '送任用社交及娛樂數據組合',
          '豁免$216行政費'
        ],
        'desc-en': [
          'Unconditional service contract cancellation within the 1st billing month ',
          'FREE 1st & 5th Monthly Fees',
          'FREE 7GB Mainland China and Macau Data',
          'Free Unlimited Social & Entertainment Data Pack (Value: $38/month)',
          'Waiver of $216 administration fee'
        ],
        url: {
          tc: 'https://www.hk.chinamobile.com/tc/home/<USER>/detail?commodityId=21202506261938168654993166336',
          sc: 'https://www.hk.chinamobile.com/sc/home/<USER>/detail?commodityId=21202506261938168654993166336',
          en: 'https://www.hk.chinamobile.com/en/home/<USER>/detail?commodityId=21202506261938168654993166336'
        }
      }
    ],
    questionList: [
      {
        'question-sc': '不同网速什么分别?',
        'question-tc': '唔同網速有咩分別?',
        'question-en': "What's the difference between different network speeds?",
        'answer-sc':
          '🚀5G: 4K、8K高清视频随便刷，玩手游无延迟，跟家人朋友无障碍视频通话。下载10MB文件用时：0.08秒<br/>✈️5Mbps：可流畅使用基本上网服务，看普通视频都可以绰绰有余！ 下载10MB文件用时：约16秒 <br/>🚝1Mbps：满足基本上网服务，正常使用社交媒体，刷刷朋友圈还是没有问题的！ 下载10MB文件用时：约1分钟 <br/>🚲128Kbps：有点龟速，只能发送文字消息和浏览简单的网页。下载10MB文件用时-约10分钟',
        'answer-tc':
          '🚀5G: 任你刷 4K / 8K 高清影片，打機零延遲，同屋企人開視像都流暢到爆！下載 10MB 檔案只需 0.08秒<br/>✈️5Mbps：上網、睇片、開Google基本操作都夠晒 ~ 下載10MB 檔案大約 16秒<br/>🚝1Mbps：睇相、開FB / IG、用WhatsApp都冇問題~ 下載 10MB 檔案大約 1分鐘<br/>🚲128Kbps：真係慢到出晒名，只夠send文字、開吓新聞~ 下載 10MB 檔案要成 10分鐘',
        'answer-en':
          '🚀 5G: Stream 4K/8K videos effortlessly, play mobile games with zero lag, and enjoy seamless video calls. Download time for 10MB file: 0.08 seconds<br/>✈️ 5Mbps: Smooth browsing and standard video streaming. Download time for 10MB file: ~16 seconds<br/>🚝 1Mbps: Basic web browsing and social media use (e.g., scrolling feeds). Download time for 10MB file: ~1 minute<br/>🚲 128Kbps: Very slow—only supports text messages and simple web pages. Download time for 10MB file: ~10 minutes'
      },
      {
        'question-sc': '学生登记要准备什么？',
        'question-tc': '學生上台要準備咩?',
        'question-en': 'What do students need to register s/ash?',
        'answer-sc':
          '👌身份证件(香港身份证/港澳通行证/护照等）<br/>👌学生身份证明：<br/>- 本地学生: 本地中小学、大专院校有效学生证 /18岁以下学生可提供出世纸、儿童身份证<br/>- 内地留学生: 大专院校发出之Offer Letter、学生证或学生签证<br/>👌地址证明:租约/银行月结单/其他住址文件都可以<br/><br/>没有学生证也不用怕！只要你是29岁及以下，都可以登记上台 s/ash 5G服务计划!',
        'answer-tc':
          '👌身份證件(香港身份證/港澳通行證/護照等）<br/>👌學生身份證明：<br/>- 本地學生: 本地中小學、大專院校有效學生證/18歲以下學生可提供出世紙、兒童身份證<br/>- 內地留學生: 大專院校發出之Offer Letter、學生證或學生簽證<br/>👌地址證明:租約/銀行月結單/其他住址文件都可以<br/><br/>冇學生證都唔怕!只要你係29歲及以下，都可以登記上台 s/ash 5G服務計劃!',
        'answer-en':
          '✅ ID Document (HKID Card/Mainland Travel Permit/Passport)<br/>✅ Student Proof:<br/>- Local Students: Valid school ID from HK institutions (or birth certificate for under-18s)<br/>- Mainland Students: University offer letter/student visa<br/>✅ Address Proof: Lease/bank statement/utility bill<br/><br/>No student ID? No problem! Ages 29 or below can still sign up for s/ash 5G plans!'
      },
      {
        'question-sc': '电话卡要如何领取？',
        'question-tc': 'SIM卡點攞?',
        'question-en': 'How to get the s/ash SIM card?',
        'answer-sc':
          '你可以选择邮寄到香港地址，或者到门市/便利店自取。对于留学生，出入境口岸遍布的OK便利店或7-Eleven便利店，方便快捷，一拿即激活，马上连上网！',
        'answer-tc':
          '揀你方便嘅方式：你可以揀寄去香港地址，或者去門市/便利店親自拎～對於留學生嚟講，香港各大出入境口岸都有OK便利店同7 Eleven便利店，超方便! 便利店一拎即用，當場激活，即刻上網~',
        'answer-en':
          'You can choose to have it mailed to a Hong Kong address or pick it up at a retail store/convenience store. For mainland students, SIM cards are available at OK or 7-Eleven convenience stores located throughout immigration ports – quick and easy to pick up, activate instantly, and get online immediately!'
      },
      {
        'question-sc': '我现在正使用香港其他本地运营商上台卡/储值卡，能转台来s/ash吗？',
        'question-tc': '我用緊其他台/預付卡，想轉過嚟得唔得？',
        'question-en': 'Can I switch from another Telecom carrier/prepaid SIM?',
        'answer-sc': '当然可以！s/ash支持最早提前180天凭身份证件登记，到期后轻轻松松转台！',
        'answer-tc': '當然得啦！<br/>唔洗通知舊台<br/>網店最早可以提前180日登記轉台 ，到期後輕鬆轉!',
        'answer-en':
          'Of course! s/ash allows you to register with your ID document up to 180 days in advance, making it easy to switch when your current plan expires!'
      }
    ]
  }),
  getters: {
    isDecEnv() {
      return window.__POWERED_BY_WUJIE__
    },
    token() {
      const { utils } = useUtils()
      return window.$wujie?.props?.data?.cmhkToken || utils.getHashQueryString('cmhkToken') || sessionStorage.getItem('cmhkToken')
    },
    channel() {
      const { utils } = useUtils()
      return window.$wujie?.props?.data?.cmhkChannel || utils.getHashQueryString('cmhkChannel') || sessionStorage.getItem('cmhkChannel')
    },
    staffId() {
      const { utils } = useUtils()
      return window.$wujie?.props?.data?.staffId || utils.getHashQueryString('staffId') || sessionStorage.getItem('staffId')
    },
    ses_salesChannel() {
      let sesParams = sessionStorage.getItem('sesParams')
      return JSON.parse(sesParams || '{}').ses_salesChannel
    },
    loginUrl(): undefined | string {
      return window.$wujie?.props?.data?.loginUrl
    }
  },
  actions: {
    ...actions
  }
})
