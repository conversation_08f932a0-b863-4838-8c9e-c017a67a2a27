export enum EventBusKey {
    LOGIN = 'v:event:login', // 登录完成
    SECKILL_NOTICE_CHANGE_TIME = 'v:seckill:event:notice_change_time', // 通知秒杀组件刷新数据
    TO_PAGE = 'toPage', // 跳转页面
    WEB_HIDDEN = '__web-hidden__', // 页面隐藏
    WEB_RESUME = '__web-resume__', // 页面显示
    IFRAME_DATA_READY = 'iframeDataReady', // iframe数据加载完成（工作台使用）
    PAGE_READY = 'page_ready', // 页面渲染完成
    // 工作台用
    PLUGIN_DELETE = 'PLUGIN_DELETE', // 组件删除
    RESET_RENDERVIEW = 'RESET_RENDERVIEW', // 重新调整渲染区的大小和位置
    SETTING_DEBUG = 'SETTING_DEBUG', // 开启/关闭debug
    CHANGE_JTP_ACTIVITY_ID = 'CHANGE_JTP_ACTIVITY_ID', // crmid更新
    RELOAD_PAGE = 'RELOAD_PAGE', // ios刷新页面
}

const pubSubCache = {
    $$vuuid: 1,
    id_0: {
        // 全局列表
        $$vuuid: 1
    }
}
const messageCache = {
    $$vuuid: 1
}

function register(instance?: any) {
    try {
        instance.$$vuuid = instance.$$vuuid || pubSubCache.$$vuuid++
        pubSubCache[`id_${instance.$$vuuid}`] = {
            $$vuuid: 1
        }
    } catch (e) {
        console.log(`can't register by ${instance}`)
    }
}

function unregister(instance?: any) {
    try {
        delete pubSubCache[`id_${instance.$$vuuid}`]
    } catch (e) {
        console.log(`can't unregister by ${instance}`)
    }
}

function on(type: string, handler: any, instance?: any) {
    if (typeof instance?.$$vuuid === 'undefined') {
        // 直接挂在全局
        instance = {
            $$vuuid: 0
        }
    }
    const handleList = pubSubCache[`id_${instance.$$vuuid}`]
    if (!handleList) {
        throw Error('未注册instance')
    }
    let cache = handleList[type] || (handleList[type] = {})
    handler.$$vuuid = handler.$$vuuid || handleList.$$vuuid++
    cache[handler.$$vuuid] = handler
    checkMessage(type)
}

function checkMessage(type: string) {
    if (messageCache[type]) {
        emit(type, ...messageCache[type])
        delete messageCache[type]
    }
}

function once(type: string, handler: any, instance?: any) {
    handler.$$once = true
    on(type, handler, instance)
}

function emit(type: string, ...params: any) {
    for (const k of Object.keys(pubSubCache)) {
        const handlelist = pubSubCache[k]
        let cache = handlelist[type]
        if (!cache) {
            continue
        }
        for (const key of Object.keys(cache)) {
            const fn = cache[key]
            if (fn.$$once) {
                off(type, fn, {
                    $$vuuid: k.replace('id_', '')
                })
            }
            fn(...params)
        }
    }
}
function emitToInstance(type: string, instance: any, ...params: any) {
    const handleList = pubSubCache[`id_${instance?.$$vuuid}`]
    if (!handleList) {
        return
    }
    let cache = handleList[type]
    if (!cache) {
        return
    }
    for (const key of Object.keys(cache)) {
        const fn = cache[key]
        if (fn.$$once) {
            off(type, fn, instance)
        }
        fn(...params)
    }
}

function stickyEmit(type: string, ...params: any) {
    let hasEmit = false
    for (const k of Object.keys(pubSubCache)) {
        const handlelist = pubSubCache[k]
        let cache = handlelist[type]
        if (!cache) {
            continue
        }
        for (const key of Object.keys(cache)) {
            const fn = cache[key]
            if (fn.$$once) {
                off(type, fn, {
                    $$vuuid: k.replace('id_', '')
                })
            }
            hasEmit = true
            fn(...params)
        }
    }
    if (!hasEmit) {
        messageCache[type] = params
        return
    }
    delete messageCache[type]
}

function off(type: string, handler: any, instance?: any) {
    if (typeof instance?.$$vuuid === 'undefined') {
        // 直接挂在全局
        instance = {
            $$vuuid: 0
        }
    }
    if (!handler || !handler.$$vuuid) {
        // 全部删除
        for (const k of Object.keys(pubSubCache)) {
            const handlelist = pubSubCache[k]
            delete handlelist[type]
        }
        return
    }
    const handleList = pubSubCache[`id_${instance.$$vuuid}`]
    let cache = handleList[type] || (handleList[type] = {})
    if (!cache) {
        return
    }
    if (handler.$$vuuid in cache) {
        delete cache[handler.$$vuuid]
    }
    if (Object.keys(cache).length <= 0) {
        delete handleList[type]
    }
}

export function useEventBus() {
    return {
        register,
        unregister,
        on,
        once,
        emit,
        emitToInstance,
        stickyEmit,
        off
    }
}
