<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Share the Fun!</title>
  <!-- 分享标题 -->
  <meta property="og:title" content="Share the Fun!" />
  <!-- 分享描述 -->
  <meta property="og:description" content="Crack the eggs, get rewards! Easter limited offers are waiting for you!" />
  <!-- 分享的图片 -->
  <meta property="og:image" content="" />
  <!-- 网站名称 -->
  <meta property="og:site_name" content="Share the Fun!" />
  <!-- 内容类型，通常为 article 或 website -->
  <meta property="og:type" content="website" />
  <script>
    function getHashQueryString(name, url) {
      if (url) {
        let Reg = (new RegExp('[?|&]' + name + '=' + '([^&;]+?)(&|#|;|$)').exec(url) || [, ""])
        return decodeURIComponent(Reg[1].replace(/\+/g, '%20')) || null
      } else {
        let Reg = (new RegExp('[?|&]' + name + '=' + '([^&;]+?)(&|#|;|$)').exec(location.href) || [, ""])
        return decodeURIComponent(Reg[1].replace(/\+/g, '%20')) || null
      }
    }
    const redirect = getHashQueryString('redirect')
    location.replace(redirect)
  </script>
</head>

<body>
</body>

</html>