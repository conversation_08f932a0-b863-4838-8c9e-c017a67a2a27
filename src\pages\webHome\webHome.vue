<template>
  <div class="webHome">
    <Swiper
      class="banner-swipe"
      :modules="modules"
      :pagination="{ clickable: true }"
      :autoplay="{
        delay: 13000,
        disableOnInteraction: false
      }"
      :auto-height="true">
      <SwiperSlide>
        <img @click="showTable" :src="$imgs[`pc/kv-${$langValue.value}.jpg`]" alt="" />
      </SwiperSlide>
    </Swiper>
    <div class="menu-box">
      <Swiper
        class="menu-swiper"
        :modules="menuModules"
        :slides-per-view="'auto'"
        :space-between="21"
        :free-mode="true"
        :grab-cursor="true">
        <SwiperSlide class="menu-slide">
          <div class="menu-item" @click="scrollTo('cont1')" :style="{ backgroundImage: `url(${$imgs['pc/menu-bg1.png']})` }">
            <div class="title">
              <p>{{ $lang?.首页['s/ashie告诉你'] }}</p>
              <p>{{ $lang?.首页['了解s/ash和香港生活'] }}</p>
            </div>
          </div>
        </SwiperSlide>
        <SwiperSlide class="menu-slide">
          <div class="menu-item" @click="scrollTo('cont2')">
            <div class="swiper-custom-pagination"></div>
            <Swiper
              class="menu-swipe"
              :modules="modules"
              :pagination="{ clickable: true, el: '.swiper-custom-pagination' }"
              :autoplay="{
                delay: 2000,
                disableOnInteraction: false
              }"
              :loop="true">
              <SwiperSlide v-for="i in 4">
                <span class="tag">{{ i }}</span>
                <img :src="$imgs[`pc/menu-bg2-${i}-${lang}.png`]" />
              </SwiperSlide>
            </Swiper>
            <div class="title">
              <p>{{ $lang?.首页['学生早鸟优惠'] }}</p>
              <p>{{ $lang?.首页['持学生证录取通知书即享'] }}</p>
            </div>
          </div>
        </SwiperSlide>
        <SwiperSlide class="menu-slide">
          <div class="menu-item" @click="scrollTo('cont3')" :style="{ backgroundImage: `url(${$imgs[`pc/menu-bg3-${lang}.png`]})` }">
            <div class="title">
              <p>{{ $lang?.首页['学生专属体验卡'] }}</p>
              <p>{{ $lang?.首页['免首月及第5个月月费'] }}</p>
            </div>
          </div>
        </SwiperSlide>
        <SwiperSlide class="menu-slide">
          <div class="menu-item" @click="scrollTo('cont4')" :style="{ backgroundImage: `url(${$imgs['pc/menu-bg4.png']})` }">
            <div class="title">
              <p>{{ $lang?.首页['s/ash 5G上台推荐计划'] }}</p>
              <p>{{ $lang?.首页['荐友上台双方各获价值高达$200积分'] }}</p>
            </div>
          </div>
        </SwiperSlide>
        <SwiperSlide class="menu-slide">
          <div class="menu-item" @click="scrollTo('cont5')" :style="{ backgroundImage: `url(${$imgs['pc/menu-bg5.png']})` }">
            <div class="title">
              <p>{{ $lang?.首页['常见问题Q&A'] }}</p>
              <p>{{ $lang?.首页['Click查询上台Tips'] }}</p>
            </div>
          </div>
        </SwiperSlide>
      </Swiper>
    </div>
    <!-- 悬浮菜单 -->
    <van-sticky :offset-top="104">
      <div class="flexMenu">
        <span :class="{ active: menuIndex === 1 }" @click="scrollTo('cont1')">{{ $lang?.首页['s/ashie告诉你'] }}</span>
        <span :class="{ active: menuIndex === 2 }" @click="scrollTo('cont2')">{{ $lang?.首页['s/ash上台限时优惠'] }}</span>
        <span :class="{ active: menuIndex === 3 }" @click="scrollTo('cont3')">{{ $lang?.首页['学生专属体验卡'] }}</span>
        <span :class="{ active: menuIndex === 4 }" @click="scrollTo('cont4')">{{ $lang?.首页['s/ash 5G上台推荐计划'] }}</span>
        <span :class="{ active: menuIndex === 5 }" @click="scrollTo('cont5')">{{ $lang?.首页['常见问题Q&A'] }}</span>
      </div>
    </van-sticky>
    <!-- s/ashie 話你知 -->
    <div class="cont cont1" id="cont1">
      <div class="cont-box">
        <div class="title">{{ $lang?.首页['s/ashie告诉你'] }}</div>
        <img class="pic" :src="$imgs['pc/pic.png']" />
        <div class="tab-box">
          <div class="tab-list">
            <div class="tab-item" :class="{ active: tabMenu === 0 }" @click="tabMenu = 0">{{ $lang?.首页['什么是 s/ash?'] }}</div>
            <div class="tab-item" :class="{ active: tabMenu === 1 }" @click="tabMenu = 1">
              {{ $lang?.首页['什么是「上台」? 有什么好处?'] }}
            </div>
          </div>
          <div class="tab-list">
            <div class="tab-item" :class="{ active: tabMenu === 2 }" @click="tabMenu = 2">{{ $lang?.首页['租房要知道的二三事'] }}</div>
            <div class="tab-item" :class="{ active: tabMenu === 3 }" @click="tabMenu = 3">{{ $lang?.首页['香港生活小贴士'] }}</div>
          </div>
        </div>
        <div class="tab-cont1">
          <div class="left">
            <img :src="$imgs[`pc/cont1-tab${tabMenu}-img.png`]" />
          </div>
          <div class="right">
            <div class="title">{{ $lang?.首页['ashie告诉你'] }}</div>
            <div class="desc" v-show="tabMenu === 0" v-html="$lang?.首页['什么是s/ash答案']"></div>
            <div class="desc" v-show="tabMenu === 1" v-html="$lang?.首页['什么是「上台」? 有什么好处?答案']"></div>
            <div class="desc" v-show="tabMenu === 2" v-html="$lang?.首页['租房要知道的二三事答案']"></div>
            <div class="desc" v-show="tabMenu === 3" v-html="$lang?.首页['香港生活小贴士答案']"></div>
          </div>
        </div>
      </div>
    </div>
    <!-- s/ash上台限時優惠 -->
    <div class="cont cont2" id="cont2">
      <div class="cont-box">
        <div class="title">{{ $lang?.首页['s/ash上台限时优惠'] }}</div>
        <div class="title-small">
          <p><em>/</em>{{ $lang?.首页['多重奖赏，一次薅尽！'] }}</p>
          <span>{{ $lang?.首页['漫游数据、终端礼遇、家居宽频，通通帮你考虑到了～学生早鸟更享价值高达$400 MyLink积分！'] }}</span>
        </div>
        <img class="cont2-img" :src="$imgs[`pc/discount-list-${lang}.png`]" alt="" />
        <div class="title-small" ref="tableRef">
          <p class="pr32"><em>/</em>{{ $lang?.首页['月费服务计划（共3款）'] }}</p>
          <span>{{ $lang?.首页['S、M、L简单3档Plan，总有一款最适合你！ 另设12/24个月合约期灵活选！'] }}</span>
        </div>
        <div class="table-box">
          <img class="cont2-img" :src="$imgs[`pc/server-list-${lang}.png`]" />
          <div class="btnBox">
            <div class="btn">
              <!-- 学生跳转链接 -->
              <span @click="goUrl(`https://www.hk.chinamobile.com/${lang}/home/<USER>/detail?commodityId=21202408141823546430043328512`)">
              </span>
              <span @click="goUrl(`https://www.hk.chinamobile.com/${lang}/home/<USER>/detail?commodityId=21202408141823547627806527488`)">
              </span>
              <span @click="goUrl(`https://www.hk.chinamobile.com/${lang}/home/<USER>/detail?commodityId=21202408141823547863803236352`)">
              </span>
            </div>
            <div class="btn text">
              <!-- 非学生跳转链接 -->
              <span @click="goUrl(`https://www.hk.chinamobile.com/${lang}/home/<USER>/detail?commodityId=21202408141823442660273819648`)">
              </span>
              <span @click="goUrl(`https://www.hk.chinamobile.com/${lang}/home/<USER>/detail?commodityId=21202408141823527525165961216`)">
              </span>
              <span @click="goUrl(`https://www.hk.chinamobile.com/${lang}/home/<USER>/detail?commodityId=21202408141823540737705906176`)">
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 12月短約專項体验卡 -->
    <div class="cont cont3" id="cont3">
      <div class="cont-box">
        <div class="title-small">
          <p><em>/</em>{{ $lang?.首页['学生专享体验卡（共3款）'] }}</p>
          <span>{{ $lang?.首页['1年硕士生首选！$0月费体验5G网络，首账单月内不满意随时取消！'] }}</span>
        </div>
        <div class="plan">
          <div class="item" v-for="item in appStore.planList" :key="item.price">
            <div class="header">
              <div class="title">{{ item[`name-${$langValue.value}`] }}</div>
            </div>
            <div class="content">
              <div class="banner">
                <img :src="$imgs[`plan/${item.img}${lang}.png`]" />
              </div>
              <div class="info">
                <div class="row">
                  <span>{{ $lang?.首页['月费'] }}</span>
                  <div class="details">
                    <strong><i>HKS</i>{{ item.price }}</strong>
                  </div>
                </div>
                <div class="row">
                  <span>{{ $lang?.首页['数据用量'] }}</span>
                  <div class="details">
                    <strong>{{ item.data }}</strong>
                  </div>
                </div>
                <div class="row">
                  <span>{{ $lang?.首页['合约期限'] }}</span>
                  <div class="details">
                    <button>{{ item.month }}</button>
                  </div>
                </div>
              </div>
            </div>
            <div class="discounts">
              <p>{{ $lang?.首页['上台优惠'] }}</p>
              <ul>
                <li v-for="i in item[`desc-${$langValue.value}`]">
                  <img :src="$imgs['pc/money-icon.png']" alt="icon" />
                  {{ i }}
                </li>
                <!-- <li>
                  <img :src="$imgs['pc/give-icon.png']" alt="icon" />
                  送1GB中國內地+澳門數據
                </li>
                <li>
                  <img :src="$imgs['pc/give-icon.png']" alt="icon" />
                  送任用社交及娛樂數據組合
                </li>
                <li>
                  <img :src="$imgs['pc/give-icon.png']" alt="icon" />
                  【網點限定】豁免$32行政費
                </li> -->
              </ul>
            </div>

            <!-- 底部按钮 -->
            <div class="footer">
              <button @click="slectPlan(item.url)">{{ $lang?.首页['选择此计划'] }} ></button>
            </div>
          </div>
        </div>
        <div class="title-small">
          <p><em>/</em>{{ $lang?.首页['5G宽频限时优惠'] }}</p>
          <span>{{ $lang?.首页['1年硕士生首选！一个价钱包全新5G Wi-Fi 路由器兼任用数据, 让你全年畅享5G网络'] }}</span>
        </div>
        <div class="thali">
          <div class="left">
            <div class="tit">{{ $lang?.首页['5G 无线宽带限时优惠 $1,980 包年'] }}</div>
            <img :src="$imgs[`pc/thali-img-${$langValue.value}.png`]" />
            <p>{{ $lang?.首页['合约期限'] }}</p>
            <span>12</span>
          </div>
          <div class="right">
            <div class="desc">
              <div class="oLi">
                <img :src="$imgs['pc/start-icon.png']" />
                <b>{{ $lang?.首页['限时优惠 $1,980 包年，新来港学生首选'] }}</b>
              </div>
              <div class="oLi">
                <img :src="$imgs['pc/start-icon.png']" />
                <b>{{ $lang?.首页['每月200GB 全速数据*'] }}</b>
              </div>
              <div class="oLi">
                <img :src="$imgs['pc/money-icon.png']" />
                <span>{{ $lang?.首页['一口价连5G CPE 5 Wi-Fi 6 路由器乙部 (建议零售价︰HK$2,488)'] }}</span>
              </div>
            </div>
            <div class="buy">
              <i>{{ $lang?.首页['一口价'] }}</i>
              <div class="price">
                <span class="mini">HKD</span>
                <span>1980</span>
              </div>
              <div class="old-price">HK$4,144</div>
              <button @click="open5GPlan">{{ $lang?.首页['选择此计划'] }} ></button>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 薦友上台有賞 -->
    <div class="cont cont4" id="cont4">
      <div class="cont-box">
        <div class="title">{{ $lang?.首页['s/ash 5G上台推荐计划'] }}</div>
        <div class="title-small">
          <p><em>/</em>{{ $lang?.首页['第1重赏'] }}</p>
        </div>
        <div class="step">
          <p class="title">{{ $lang?.首页['推荐上台三部曲'] }}</p>
          <div class="step-list">
            <div class="item">
              <p class="name">{{ $lang?.首页['符合推荐人资格'] }}</p>
              <p class="desc">{{ $lang?.首页['现有s/ash 5G服务计划客户'] }}</p>
            </div>
            <div class="item">
              <p class="name">{{ $lang?.首页['填写推荐人s/ash电话号码'] }}</p>
              <p class="desc">{{ $lang?.首页['被推荐人网店/微信小程序上台时填写推荐人的s/ash 5G服务计划电话号码'] }}</p>
            </div>
            <div class="item">
              <p class="name">{{ $lang?.首页['成功激活号码双方拎奖赏'] }}</p>
              <p class="desc" v-html="$lang?.首页['成功激活号码双方拎奖赏内容']"></p>
            </div>
          </div>
          <div class="table-box">
            <div class="table">
              <div class="tr">
                <div class="td">{{ $lang?.首页['被推荐人成功激活'] }}</div>
                <div class="td">{{ $lang?.首页['签订12个合约'] }}</div>
                <div class="td">{{ $lang?.首页['签订24个合约'] }}</div>
              </div>
              <div class="tr">
                <div class="th">{{ $lang?.首页['s/ash5G 服务计划30GB/60GB/100GB'] }}</div>
                <div class="th">
                  <p>{{ $lang?.首页['被推荐人可获'] }}：MyLink <i>10,000</i>{{ $lang?.首页['分'] }}</p>
                  <p>{{ $lang?.首页['推荐人可获'] }}：MyLink <i>10,000</i>{{ $lang?.首页['分'] }}</p>
                </div>
                <div class="th">
                  <p>{{ $lang?.首页['被推荐人可获'] }}：MyLink <i>20,000</i>{{ $lang?.首页['分'] }}</p>
                  <p>{{ $lang?.首页['推荐人可获'] }}：MyLink <i>20,000</i>{{ $lang?.首页['分'] }}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="title-small">
          <p><em>/</em>{{ $lang?.首页['第2重赏'] }}</p>
        </div>
        <div class="step">
          <p class="title">{{ $lang?.首页['分享s/ash三部曲'] }}</p>
          <div class="step-list share">
            <div class="item">
              <p class="name">{{ $lang?.首页['小红书或Instagram分享推荐s/ash'] }}</p>
            </div>
            <div class="item">
              <p class="name">{{ $lang?.首页['扫码登记并上传帖文截图'] }}</p>
            </div>
            <div class="item">
              <p class="name">
                {{ $lang?.首页['合资格分享者可获10,000 MyLink积分。积分将于该活动结束后的90个自然日内派发至合资格参加者的MyLink账户中'] }}
              </p>
            </div>
          </div>
        </div>
        <div class="share-code">
          <div class="code">
            <img :src="$imgs['share-code.png']" />
          </div>
          <div class="share-nr">
            <p>{{ $lang?.首页['扫一扫'] }}</p>
            <span>{{ $lang?.首页['浏览活动详情并上传截图'] }}</span>
          </div>
        </div>
        <p class="see-rule" @click="openRule">{{ $lang?.首页['s/ash 5G上台推荐计划受本条款及细则约束'] }}</p>
      </div>
    </div>
    <!-- 常見問題Q&A -->
    <div class="cont cont5" id="cont5">
      <div class="cont-box">
        <div class="title">{{ $lang?.首页['常见问题Q&A'] }}</div>
        <div class="list">
          <div class="item" v-for="item in appStore.questionList" :key="item['question-sc']">
            <div class="q">
              <em>Q</em>
              <p>{{ item[`question-${$langValue.value}`] }}</p>
              <div class="triangle"></div>
            </div>
            <div class="a" v-html="item[`answer-${$langValue.value}`]"></div>
          </div>
        </div>
      </div>
    </div>
    <Chat />
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, onBeforeUnmount } from 'vue'
import { useAppStore } from '@/store'
import { useLang, useEventBus, useUtils, useGTM } from '@/hook'
import { Swiper, SwiperSlide } from 'swiper/vue'
import { Pagination, Autoplay, FreeMode } from 'swiper/modules'
import { inApp } from '@via/mylink-sdk'
import Chat from '@/components/chat/index.vue'
import 'swiper/css'
import 'swiper/css/autoplay'
import 'swiper/css/pagination'
import 'swiper/css/free-mode'
const modules = [Pagination, Autoplay]
const menuModules = [FreeMode]
const menuIndex = ref(1)
const { lang } = useLang()
const appStore = useAppStore()
let observer: IntersectionObserver | null = null

const goUrl = (url: string) => {
  if (!appStore.isDecEnv) {
    if (inApp.value) {
      location.href = `openurl-modal://${url}`
    } else {
      window.open(url)
    }
  } else {
    window.open(url)
  }
}

const openRule = () => {
  goUrl(
    'https://www.hk.chinamobile.com/upload/onlineshop/2025-07-03/slash5gSubscriptionReferralProgramandXiaohongshuInstagramShareslashtoEarnPointsTermsandConditions.pdf'
  )
}

const slectPlan = (url) => {
  goUrl(url[lang.value])
}

const open5GPlan = () => {
  goUrl(`https://www.hk.chinamobile.com/${lang.value}/home-family/5g-broadband`)
}

const setupObserver = () => {
  observer = new IntersectionObserver(
    (entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          const id = entry.target.id
          if (id.startsWith('cont')) {
            menuIndex.value = parseInt(id.replace('cont', ''))
          }
        }
      })
    },
    {
      threshold: 0.1, // 当50%的section进入视口时触发
      rootMargin: '-250px 0px 0px 0px' // 减去悬浮菜单高度
    }
  )

  // 观察所有section
  document.querySelectorAll('[id^="cont"]').forEach((element) => {
    observer?.observe(element)
  })
}

onMounted(() => {
  setupObserver()
  appStore.initScrollSettings()
})

onBeforeUnmount(() => {
  observer?.disconnect()
})
const tabMenu = ref(0)
const tableRef = ref<HTMLDivElement | null>(null)
const showTable = () => {
  appStore.scrollToRef(tableRef.value)
}

const scrollTo = (id: string) => {
  // PC端的偏移量：104 (sticky菜单) + 123 (其他) = 227
  appStore.scrollToElement(id, 227)
}
</script>

<style scoped lang="less">
@import './webHome.less';
</style>
