<script lang="tsx">
import { defineComponent, VNode, KeepAlive, watch } from 'vue'
import { useRouter } from 'vue-router'
import Toast from '@/components/Toast/toast.vue'
import Loading from '@/components/Loading.vue'
import { useAppStore } from '@/store'

export default defineComponent({
  name: 'App',
  setup() {
    const router = useRouter()
    const appStore = useAppStore()
    appStore.checkIsMobild()
    appStore.getWxEnv()
    // watch(
    //   () => appStore.isBlackFridayEndTime,
    //   () => {
    //     if (appStore.isBlackFridayEndTime) {
    //       document.title = 'BLACK FRIDAY'
    //       parent.document.title = 'BLACK FRIDAY'
    //     }
    //   }
    // )
    return () => (
      <router-view>
        {{
          default: (scope: { Component: VNode }) => (
            <>
              <KeepAlive>{router.currentRoute.value.meta.keepAlive && scope.Component}</KeepAlive>
              {!router.currentRoute.value.meta.keepAlive && scope.Component}
              <Toast></Toast>
              <Loading />
            </>
          )
        }}
      </router-view>
    )
  }
})
</script>
<style lang="less">
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  outline: none;
  border: none;
}

html,
body {
  -webkit-tap-highlight-color: transparent;
  // -webkit-touch-callout: none;
  -webkit-appearance: none;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

img {
  width: 100%;
  display: block;
}

li {
  list-style: none;
}

#app {
  // max-width: 750px;
  margin: auto;
  position: relative;
}

.pc {
  overflow-x: hidden !important;
  .van-toast--text {
    padding: 0;
    font-size: 16pw;
    display: flex;
    align-items: center;
    justify-content: center;
    line-height: normal;
    padding: 10pw;
    border-radius: 10pw;
  }
}
</style>
