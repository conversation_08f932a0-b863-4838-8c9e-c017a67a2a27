{"compilerOptions": {"noImplicitAny": false, "target": "esnext", "useDefineForClassFields": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "strict": true, "allowJs": true, "jsx": "preserve", "outDir": "lib", "sourceMap": true, "esModuleInterop": true, "declaration": true, "declarationDir": "types", "lib": ["esnext", "dom"], "allowSyntheticDefaultImports": true, "experimentalDecorators": true, "baseUrl": "./", "paths": {"@/*": ["src/*"]}}, "include": ["src/**/*.ts", "src/**/*.d.ts", "src/**/*.tsx", "src/**/*.vue", "*.d.ts"], "exclude": ["node_modules"]}