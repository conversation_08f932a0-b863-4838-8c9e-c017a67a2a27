export type EnvConfig = Record<
  'develop' | 'uat' | 'beta' | 'production',
  {
    API_HOST: string
    CDN_BASE: string
    BASE: string
    DEFAULT_TOKEN?: string
    LOG_ENV?: string
    MYLINK_SDK_URL: string
    OMNI_API_SDK_ENV: string
    OMNI_PARTNER_TOKEN: string
    ArmasPid?: string
    RUN_ENV?: 'develop' | 'uat' | 'beta' | 'production'
    ACTIVITY_NAME?: string
    version: string
    chinamobile_url: string
    OMNI_BASE: string
    frontendConfigUrl: string
    jsonConfigUrl: string
  }
>
const envConfig: EnvConfig = {
  develop: {
    API_HOST: 'https://omniapi-uat.hk.chinamobile.com/viaActivity/external/api/slash-youth-zone-be',
    CDN_BASE: 'https://uatoss.mylinkapp.hk/via/atv/',
    BASE: './',
    DEFAULT_TOKEN: 'efb4fa9e-a251-4aca-9e93-713bf176fc77',
    //mylink-SDK地址
    MYLINK_SDK_URL: '/js/<EMAIL>',
    OMNI_API_SDK_ENV: 'UAT',
    OMNI_PARTNER_TOKEN: '4c5892cd097244c5892cd097240099zx',
    version: 'v1.0.0-dev.0',
    chinamobile_url: 'https://www-uat.hk.chinamobile.com',
    OMNI_BASE: 'https://omniapi-uat.hk.chinamobile.com',
    jsonConfigUrl:
      'https://via-k8s-uat.mylinkapp.hk/gateway/via-activity-cms-be/activity/public/164141e32483499c8eb345d1600721c7/getActivityUiConfig',
    frontendConfigUrl:
      'https://via-k8s-uat.mylinkapp.hk/client-gateway/via-activity-cms-be/api/activity/public/164141e32483499c8eb345d1600721c7/frontend-config'
  },
  uat: {
    API_HOST: 'https://omniapi-uat.hk.chinamobile.com/viaActivity/external/api/slash-youth-zone-be',
    CDN_BASE: './',
    BASE: './',
    LOG_ENV: 'daily',
    //mylink-SDK地址
    MYLINK_SDK_URL: 'https://omniapi-uat.hk.chinamobile.com/viaActivity/external/tc/slash-youth-zone/js/<EMAIL>',
    OMNI_API_SDK_ENV: 'UAT',
    OMNI_PARTNER_TOKEN: '4c5892cd097244c5892cd097240099zx',
    version: 'v1.0.0-uat.0',
    chinamobile_url: 'https://www-uat.hk.chinamobile.com',
    OMNI_BASE: 'https://omniapi-uat.hk.chinamobile.com',
    jsonConfigUrl:
      'https://via-k8s-uat.mylinkapp.hk/gateway/via-activity-cms-be/activity/public/164141e32483499c8eb345d1600721c7/getActivityUiConfig',
    frontendConfigUrl:
      'https://via-k8s-uat.mylinkapp.hk/client-gateway/via-activity-cms-be/api/activity/public/164141e32483499c8eb345d1600721c7/frontend-config'
  },
  beta: {
    API_HOST: 'https://omniapi.hk.chinamobile.com/viaActivity/external/api/slash-youth-zone-be',
    CDN_BASE: './',
    BASE: './',
    LOG_ENV: 'gray',
    MYLINK_SDK_URL: 'https://omniapi.hk.chinamobile.com/viaActivity/external/tc/slash-youth-zone/js/<EMAIL>',
    ArmasPid: 'jlhn8qavag@df79dc7cc1c2d45',
    OMNI_API_SDK_ENV: 'PROD',
    OMNI_PARTNER_TOKEN: '4c5892cd097244c5892cd097241420mg',
    version: 'v1.0.0-beta.0',
    chinamobile_url: 'https://www.hk.chinamobile.com',
    OMNI_BASE: 'https://omniapi.hk.chinamobile.com',
    jsonConfigUrl:
      'https://activity.mylinkapp.hk/gateway/via-activity-cms-be/activity/public/e209cb12384f4de5815a961fbf539c55/getActivityUiConfig',
    frontendConfigUrl:
      'https://activity.mylinkapp.hk/client-gateway/via-activity-cms-be/api/activity/public/08d03beae2d14b7bad5f69a4f7814d47/frontend-config'
  },
  production: {
    API_HOST: 'https://omniapi.hk.chinamobile.com/viaActivity/external/api/slash-youth-zone-be',
    CDN_BASE: './',
    BASE: './',
    LOG_ENV: 'prod',
    MYLINK_SDK_URL: 'https://omniapi.hk.chinamobile.com/viaActivity/external/tc/slash-youth-zone/js/<EMAIL>',
    ArmasPid: 'jlhn8qavag@df79dc7cc1c2d45',
    OMNI_API_SDK_ENV: 'PROD',
    OMNI_PARTNER_TOKEN: '4c5892cd097244c5892cd097241420mg',
    version: 'v1.0.0',
    chinamobile_url: 'https://www.hk.chinamobile.com',
    OMNI_BASE: 'https://omniapi.hk.chinamobile.com',
    jsonConfigUrl:
      'https://activity.mylinkapp.hk/gateway/via-activity-cms-be/activity/public/e209cb12384f4de5815a961fbf539c55/getActivityUiConfig',
    frontendConfigUrl:
      'https://activity.mylinkapp.hk/client-gateway/via-activity-cms-be/api/activity/public/08d03beae2d14b7bad5f69a4f7814d47/frontend-config'
  }
}

const env = process.env.runEnv as 'develop' | 'uat' | 'beta' | 'production'
console.log('当前运行环境：', env)

export function useEnvConfig() {
  if (!envConfig[env]) {
    throw '当前运行环境不存在'
  }
  const config: EnvConfig['production'] = {
    ...envConfig[env],
    RUN_ENV: env,
    ACTIVITY_NAME: 'slash-youth-zone'
  }
  const keys = Object.keys(config)
  for (let i = 0; i < keys.length; ++i) {
    let key = `VITE_APP_${keys[i]}`
    process.env[key] = config[keys[i]]
  }
  console.log('当前环境变量：', config)
  return config
}
