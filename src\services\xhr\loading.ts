import { ToastWrapperInstance } from 'vant'
let loadingToast: ToastWrapperInstance
interface Config {
    allRequest: Record<string,string>
    hasLoading: boolean
    pushRequest: (key:string,url:string) => void
    finishRequest: (key:string) => void
}
class Loading implements Config {
    allRequest: Record<string,string>
    hasLoading: boolean
    constructor(){
        this.allRequest = {}
        this.hasLoading = false
    }
    pushRequest(key:string,url:string){
        this.allRequest[key] = url
        if(!this.hasLoading){
            console.log('hi')
            this.hasLoading = true
            loadingToast = showLoadingToast({
                duration: 0
            })
        }
        console.log('当前队列中的请求',{...this.allRequest})
    }
    finishRequest(key:string){
        Reflect.deleteProperty(this.allRequest,key)
        if(Object.keys(this.allRequest).length == 0){
            this.hasLoading = false
            loadingToast.close()
        }
        console.log('剩余请求',{...this.allRequest})
    }
}
export default new Loading()