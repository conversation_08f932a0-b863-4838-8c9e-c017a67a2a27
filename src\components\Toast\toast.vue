<template>
    <transition name="fade">
        <div :class="appStore.isPC ? 'web-toast' : 'toast'" v-if="appStore.toastMessage">
            {{ appStore.toastMessage }}
        </div>
    </transition>
</template>

<script lang="ts" setup>
import { watch } from "vue";
import { useAppStore } from "@/store";
const appStore = useAppStore()
watch(()=>appStore.toastMessage,()=>{
    if (appStore.toastMessage) {
        setTimeout(() => {
            appStore.toastMessage = ''
        }, 1500);
    }
})
</script>

<style scoped>
.fade-enter-active,
.fade-leave-active {
    transition: opacity 0.5s;
}

.fade-enter,
.fade-leave-to {
    opacity: 0;
}

.toast {
    /* Add your styles here */
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    padding: 24px;
    background: rgba(0, 0, 0, 0.6);
    border-radius: 24px 24px 24px 24px;
    color: #fff;
    z-index: 1000;
    display: flex;
    align-items: center;
    font-size: 32px;
    max-width: 80%;
    box-sizing: border-box;
    text-align: center;
}

.web-toast {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    padding: 8px 16PX;
    background: rgba(0, 0, 0, 0.6);
    border-radius: 10PX;
    color: #fff;
    z-index: 1000;
    display: flex;
    align-items: center;
    font-size: 12PX;
    max-width: 80%;
    box-sizing: border-box;
    text-align: center
}
</style>