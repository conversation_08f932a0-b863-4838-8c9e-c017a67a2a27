.webHome {
  .banner-swipe {
    width: 100%;
    aspect-ratio: 1920 / 730;
    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }
  .flexMenu {
    display: flex;
    position: fixed;
    width: 100%;
    height: 80pw;
    z-index: 100;
    background: white;
    border-bottom: 1px solid #e0e0e0;
    span {
      cursor: pointer;
      flex: 1;
      font-size: 26pw;
      color: rgba(38, 39, 41, 0.8);
      text-align: center;
      display: flex;
      align-items: center;
      justify-content: center;
      border: 1px solid #e0e0e0;
      &.active {
        color: white;
        background: #00a7ff;
      }
    }
  }
  .menu-box {
    width: 1728pw;
    margin: 0 auto;
    padding: 80pw 0;
    max-width: calc(4 * 416pw + 3 * 21pw + 120pw); /* 4个完整项目 + 3个间隙 + 第五个项目的部分宽度(约30%) */
    .menu-swiper {
      overflow: visible;
    }
    .menu-slide {
      width: 416pw !important;

      &:nth-of-type(1) {
        .menu-item .title {
          background: linear-gradient(180deg, rgba(0, 167, 255, 0) 0%, #00A7FF 70%);
        }
      }
      &:nth-of-type(2) {
        .menu-item .title {
          background: linear-gradient(180deg, rgba(235, 110, 165, 0) 0%, #eb6ea5 70%);
        }
      }
      &:nth-of-type(3) {
        .menu-item .title {
          background: linear-gradient(180deg, rgba(104, 173, 74, 0) 0%, #AB4BFF 70%);
        }
      }
      &:nth-of-type(4) {
        .menu-item .title {
          background: linear-gradient(180deg, rgba(0, 206, 255, 0) 0%, #68AD4A 70%);
        }
      }
      &:nth-of-type(5) {
        .menu-item .title {
          background: linear-gradient(180deg, rgba(0, 167, 255, 0) 0%, #00CEFF 70%);
        }
      }
    }
    .menu-item {
      width: 416pw;
      height: 480pw;
      overflow: hidden;
      border-radius: 21pw;
      position: relative;
      background-size: 100% 100%;
      background-repeat: no-repeat;
      &:hover {
        transform: translateY(-10px);
        transition: all 0.3s;
        box-shadow: 0 10pw 5pw rgba(0, 0, 0, 0.3);
      }
      :deep(.swiper-slide) {
        border-radius: 21pw;
        overflow: hidden;
      }
      :deep(.swiper-custom-pagination) {
        position: absolute;
        z-index: 10;
        bottom: 40%;
        left: 0;
        right: 0;
        margin: auto;
        text-align: center;
        z-index: 10;
        .swiper-pagination-bullet {
          width: 11pw;
          height: 11pw;
          background: black;
        }
        .swiper-pagination-bullet-active {
          width: 24pw;
          height: 11pw;
          background: #ffffff;
          border-radius: 16pw;
        }
      }
      .menu-swipe {
        width: 416pw;
        height: 480pw;
        .tag {
          position: absolute;
          top: 0;
          left: 0;
          width: 64pw;
          height: 64pw;
          background: rgba(38, 39, 41, 0.2);
          border-radius: 21pw 0 21pw 0;
          font-size: 27pw;
          color: #ffffff;
          display: flex;
          align-items: center;
          justify-content: center;
        }
        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }
      .title {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        width: 416pw;
        height: 260pw;
        border-radius: 21pw;
        z-index: 1;
        p {
          &:nth-of-type(1) {
            font-size: 30pw;
            color: #ffffff;
            padding-bottom: 11pw;
          }
          &:nth-of-type(2) {
            text-align: center;
            font-size: 24pw;
            color: rgba(255, 255, 255, 0.8);
          }
        }
      }

    }
  }
  .cont {
    .cont-box {
      padding: 80pw 0;
      width: 1728pw;
      margin: 0 auto;
      > .title {
        font-size: 43pw;
        color: #2f85ff;
        padding-bottom: 60pw;
      }
    }
    &.cont1 {
      position: relative;
      .cont-box {
        background: url('@/assets/imgs/pc/cont1-bg.png') no-repeat center center;
        background-size: 100%;
      }
      .pic {
        width: 189pw;
        height: 189pw;
        position: absolute;
        top: 104pw;
        right: 65pw;
      }
      .tab-box {
        .tab-list {
          display: flex;
          align-items: center;
          margin-bottom: 43pw;
          &:nth-of-type(1) {
            margin-left: 429pw;
          }
          &:nth-of-type(2) {
            margin-left: 648pw;
          }
        }
        .tab-item {
          padding: 20pw 43pw;
          background: #ffffff;
          border-radius: 64pw 21pw 0 64pw;
          border: 1px solid #2f85ff;
          font-size: 24pw;
          color: #2f85ff;
          margin-right: 133pw;
          cursor: pointer;
          &.active {
            background: #2f85ff;
            color: #ffffff;
          }
        }
      }
      .tab-cont1 {
        display: flex;
        justify-content: center;
        .left {
          width: 575pw;
          height: 431pw;
          img {
            width: 575pw;
            height: 431pw;
          }
        }
        .right {
          flex: 1;
          margin-left: 64pw;
          .title {
            font-size: 27pw;
            color: rgba(38, 39, 41, 0.8);
            padding: 39pw 0 11pw;
          }
          .desc {
            padding: 32pw;
            font-size: 27pw;
            color: rgba(38, 39, 41, 0.6);
            line-height: 40pw;
            box-shadow: 0 5pw 21pw 0 rgba(0, 0, 0, 0.08);
            border-radius: 0 21pw 21pw 21pw;
          }
        }
      }
    }
    &.cont2 {
      background: #f4f9ff;
      .title-small {
        display: flex;
        align-items: center;
        padding-bottom: 43pw;
        flex-wrap: wrap;
        p {
          font-size: 32pw;
          color: #262729;
          em {
            color: #2f85ff;
            padding-right: 16pw;
          }
        }
        span {
          display: block;
          font-size: 27pw;
          color: rgba(38, 39, 41, 0.6);
        }
      }
      .table-box {
        position: relative;
        .btnBox {
          position: absolute;
          left: 275pw;
          right: 0;
          top: 200pw;
          .btn {
            display: flex;
            align-items: center;
            justify-content: space-around;
            span {
              width: 300pw;
              height: 77pw;
              display: block;
              cursor: pointer;
            }
            &.text{
              margin-top: 10pw;
              span{
                height:50pw;
              }
            }
            a{
              width: 100%;
              height: 100%;
              display: block;
            }
          }
        }
      }
      .cont2-img {
        padding-bottom: 53pw;
      }
    }
    &.cont3 {
      background: #f4f9ff;
      .cont-box {
        padding-top: 0;
      }
      .title-small {
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        padding-bottom: 43pw;
        p {
          font-size: 32pw;
          color: #262729;
          em {
            color: #2f85ff;
            padding-right: 16pw;
          }
        }
        span {
          display: block;
          font-size: 27pw;
          color: rgba(38, 39, 41, 0.6);
        }
      }
      .plan {
        margin-bottom: 53pw;
        display: flex;
        align-items: center;
        justify-content: space-around;
        gap: 24pw;
        .item {
          flex: 1;
          background-color: #f9f9f9;
          border-radius: 12px;
          box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
          overflow: hidden;
        }
        .header {
          height: 64pw;
          display: flex;
          align-items: center;
          padding-left: 36pw;
          background: linear-gradient(to right, #007bff, #00e676);
          .title {
            font-size: 27pw;
            color: #fff;
            font-weight: bold;
          }
        }

        .content {
          .banner {
            height: 164pw;
            width: 100%;
          }
          .info {
            padding: 0 32pw;
            margin-top: 16px;
            display: flex;
            gap: 60pw;
            .row {
              &:first-child {
                .details {
                  strong {
                    color: #d5197f;
                  }
                }
              }
              flex: 1;
              width: (1/3)*100%;
              display: flex;
              flex-direction: column;
              justify-content: space-around;
              font-size: 12px;
              color: #666;
              span {
                font-size: 21pw;
                color: rgba(38, 39, 41, 0.6);
                text-align: center;
              }
              .details {
                margin-top: 20pw;
                display: flex;
                align-items: center;
                justify-content: center;
                strong {
                  font-family: Saira ExtraCondensed, Saira ExtraCondensed;
                  font-weight: bold;
                  font-size: 36pw;
                  color: #262729;
                  i {
                    font-size: 18pw;
                    font-style: normal;
                  }
                }

                button {
                  width: 91pw;
                  height: 48pw;
                  background: #f5f5f5;
                  border-radius: 32pw;
                  border: 1px solid #00a7ff;
                  font-size: 27pw;
                  color: #00a7ff;
                }
              }
            }
          }
        }

        .discounts {
          padding: 32pw;
          p {
            width: 100%;
            overflow: hidden;
            font-size: 21pw;
            color: #666;
            margin-bottom: 32pw;
            position: relative;
            &::after {
              position: absolute;
              top: 0;
              right: 0;
              bottom: 0;
              margin: auto;
              height: 1px;
              content: '';
              display: inline-block;
              width: 380pw;
              border-top: 1px solid rgba(38, 39, 41, 0.2);
            }
          }
          ul {
            padding: 0;
            li {
              &:first-child {
                font-size: 32pw;
                color: #000000;
                i {
                  font-style: normal;
                  color: #eb6ea5;
                }
              }
              &:last-child {
                margin-bottom: 0;
              }
              display: flex;
              align-items: center;
              margin-bottom: 35pw;
              font-size: 27pw;
              color: rgba(38, 39, 41, 0.8);
              img {
                width: 43pw;
                height: 43pw;
                margin-right: 16pw;
              }
            }
          }
        }
        .footer {
          text-align: center;
          padding-bottom: 35pw;
          button {
            width: 496pw;
            height: 77pw;
            border: none;
            background: #00a7ff;
            border-radius: 53pw;
            cursor: pointer;
            font-weight: 400;
            font-size: 32pw;
            color: #ffffff;
          }
        }
      }
      .thali {
        display: flex;
        background: #ffffff;
        box-shadow: 0px 5pw 21pw 0px rgba(213, 217, 255, 0.25);
        border-radius: 21pw;
        .left {
          display: flex;
          flex-direction: column;
          width: 545pw;
          box-shadow: 0px 5pw 21pw 0px rgba(0, 0, 0, 0.08);
          border-radius: 21pw;
          overflow: hidden;
          background: #ffffff;
          padding-bottom: 77pw;
          margin-right: 32pw;
          .tit {
            width: 545pw;
            height: 64pw;
            line-height: 64pw;
            background: linear-gradient(90deg, #4dc610 0%, #b8e823 100%);
            font-weight: bold;
            font-size: 21pw;
            color: #ffffff;
            padding-left: 36pw;
          }
          > img {
            width: 100%;
          }
          p {
            padding: 32pw;
            font-size: 27pw;
            color: rgba(38, 39, 41, 0.6);
          }
          span {
            width: 91pw;
            height: 48pw;
            line-height: 48pw;
            text-align: center;
            display: block;
            font-size: 27pw;
            color: #2f85ff;
            border: 1px solid #2f85ff;
            border-radius: 100pw;
            background: #f5f5f5;
            margin-left: 32pw;
          }
        }
        .right {
          flex: 1;
          display: flex;
          padding-right: 64pw;
          padding-top: 32pw;
          padding-bottom: 32pw;
          .desc {
            flex: 1;
            margin-right: 50px;
            .oLi {
              display: flex;
              align-items: baseline;
              margin-bottom: 32pw;
              &:last-child {
                margin-bottom: 0;
              }
              img {
                width: 43pw;
                height: 43pw;
                margin-right: 16pw;
                transform: translateY(12pw);
              }
              i {
                font-style: normal;
                color: #eb6ea5;
              }
              span {
                font-size: 27pw;
                color: rgba(38, 39, 41, 0.8);
              }
              b {
                font-size: 32pw;
                color: #262729;
              }
            }
          }
          .buy {
            display: flex;
            align-items: center;
            justify-content: space-between;
            flex-direction: column;
            i {
              display: block;
              font-size: 32pw;
              color: rgba(38, 39, 41, 0.6);
              text-align: center;
              font-style: normal;
            }
            .price {
              min-height: 125pw;
              padding: 11pw 0;
              span {
                letter-spacing: 0;
                font-size: 80pw;
                color: #d5197f;
                font-weight: 800;
                font-family: 'SairaExtraCondensed-Bold';
                &.mini {
                  font-size: 48pw;
                }
              }
            }
            .old-price {
              font-size: 32pw;
              color: rgba(38, 39, 41, 0.6);
              text-align: center;
              font-style: normal;
              text-decoration-line: line-through;
              padding-bottom: 32pw;
            }
            button {
              width: 300pw;
              height: 77pw;
              background: #2f85ff;
              border-radius: 48pw;
              display: flex;
              align-items: center;
              justify-content: center;
              font-weight: 400;
              font-size: 32pw;
              color: #ffffff;
              cursor: pointer;
            }
          }
        }
      }
    }
    &.cont4 {
      background: white;
      .title-small {
        p {
          display: flex;
          align-items: baseline;
          flex-wrap: wrap;
          padding-bottom: 43pw;
          font-size: 32pw;
          color: #262729;
          em {
            font-size: 44pw;
            display: block;
            color: #2f85ff;
            padding-right: 16pw;
          }
          i {
            padding: 0 8pw;
            display: block;
            font-weight: bold;
            font-style: normal;
            font-size: 64pw;
            color: #00a7ff;
            transform: translateY(5pw);
            font-family: DINPro, DINPro;
          }
        }
      }
      .step {
        width: 1728pw;
        margin: 0 auto;
        .title {
          text-align: center;
          padding: 56pw 0 43pw;
          font-size: 32pw;
          color: rgba(38, 39, 41, 0.8);
        }
        .step-list {
          width: calc(1728pw - 98pw);
          margin: 0 auto;
          display: flex;
          justify-content: space-between;
          background: url('@/assets/imgs/pc/step-list1-bg.png') no-repeat center top;
          background-size: 100% auto;
          &.share {
            background: url('@/assets/imgs/pc/step-list2-bg.png') no-repeat center top;
            background-size: 100% auto;
            .item {
              min-height: 199pw;
              width: 461pw;
            }
          }
          .item {
            width: 421pw;
            text-align: center;
            padding: 0 20pw;
            .name {
              font-size: 32pw;
              color: rgba(38, 39, 41, 0.8);
              padding: 110pw 0 10pw;
            }
            .desc {
              font-size: 27pw;
              color: rgba(38, 39, 41, 0.6);
            }
          }
        }
        .table-box {
          margin: 43pw auto;
          .table {
            display: table;
            width: 100%;
            border-collapse: collapse;
            border-radius: 21pw 21pw 0 0;
            overflow: hidden;
            .tr {
              display: table-row;
            }
            .td,
            .th {
              display: table-cell;
              border: 1px solid rgba(38, 39, 41, 0.2);
              padding: 8px;
              text-align: center;
              vertical-align: middle;
            }
            .td {
              background: #00a7ff;
              font-size: 32pw;
              color: #ffffff;
            }
            .th {
              font-size: 27pw;
              color: rgba(38, 39, 41, 0.8);
              padding: 55pw;
              &:first-child {
                background: #f4f9ff;
              }
              i {
                color: #eb6ea5;
                font-style: normal;
              }
            }
          }
        }
      }
      .share-code {
        width: 469pw;
        background: rgba(26, 213, 134, 0.05);
        border-radius: 21pw;
        margin: 43pw auto 21pw;
        padding: 25pw;
        display: flex;
        align-items: center;
        .code {
          width: 179pw;
          height: 179pw;
          border-radius: 21pw;
          overflow: hidden;
          img {
            width: 100%;
            height: 100%;
          }
        }
        .share-nr {
          margin-left: 30pw;
          flex: 1;
          p {
            font-size: 32pw;
            color: rgba(38, 39, 41, 0.8);
            margin-bottom: 11pw;
          }
          span {
            font-size: 27pw;
            color: rgba(38, 39, 41, 0.6);
          }
        }
      }
      .see-rule {
        font-size: 27pw;
        color: rgba(38, 39, 41, 0.6);
        text-align: center;
        text-decoration-line: underline;
        text-transform: none;
        cursor: pointer;
      }
    }
    &.cont5 {
      background: #f4f9ff;
      .list {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 43pw;
        .item {
          &:nth-child(2n-1) {
            width: 960pw;
          }
          &:nth-child(2n) {
            flex: 1;
          }
          &:nth-child(4n + 1),
          &:nth-child(4n + 4) {
            .q {
              background-color: #00a7ff;
            }
          }

          &:nth-child(4n + 2),
          &:nth-child(4n + 3) {
            .q {
              background-color: #1ad586;
              .triangle {
                border-top: 26pw solid #1ad586;
              }
            }
          }
          .q {
            width: 100%;
            min-height: 152pw;
            background: #00a7ff url('@/assets/imgs/pc/icon.png') no-repeat 96% top;
            background-size: contain;
            display: flex;
            align-items: center;
            padding: 30pw;
            border-radius: 21pw 21pw 0 0;
            position: relative;
            .triangle {
              width: 0;
              height: 0;
              border-left: 26pw solid transparent;
              border-right: 26pw solid transparent;
              border-top: 26pw solid #00a7ff;
              position: absolute;
              left: 0;
              right: 0;
              margin: auto;
              bottom: -15pw;
            }
            em {
              display: block;
              font-style: normal;
              width: 91pw;
              height: 91pw;
              line-height: 91pw;
              background: #ffffff;
              border-radius: 45pw;
              font-size: 53pw;
              color: #00a7ff;
              text-align: center;
            }
            p {
              flex: 1;
              padding-left: 32pw;
              font-size: 32pw;
              color: #ffffff;
            }
          }
          .a {
            padding: 32pw;
            background: white;
            font-size: 27pw;
            color: rgba(38, 39, 41, 0.8);
            line-height: 40pw;
            border-radius: 0px 0px 21pw 21pw;
          }
        }
      }
    }
  }
}
.pr32 {
  padding-right: 32pw;
}
