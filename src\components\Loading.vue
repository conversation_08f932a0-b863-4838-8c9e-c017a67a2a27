<template>
  <div>
    <div v-if="appStore.isLoading" class="loading-overlay">
      <div :class="appStore.isPC ? 'web-loader' : 'loader'"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useAppStore } from '@/store'
const appStore = useAppStore()
</script>

<style>
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

.loader {
  border: 16px solid #f3f3f3;
  border-top: 16px solid #cccdd0;
  border-radius: 50%;
  width: 120px;
  height: 120px;
  animation: spin 0.5s linear infinite;
}

.web-loader {
  border: 10pw solid #f3f3f3;
  border-top: 10pw solid #cccdd0;
  border-radius: 50%;
  width: 50pw;
  height: 50pw;
  animation: spin 0.5s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}
</style>
