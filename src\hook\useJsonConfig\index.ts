import { reactive, computed } from 'vue'
import type { App } from 'vue'
import Activity, { IJsonParams } from "@/services/activity";

/** 全局json配置 */
const globalJsonConfig = reactive({})

/**
 * 请求json配置
 * @param _params 请求参数
 * @returns 
 */
export async function getJsonConfig(_params?: IJsonParams) {
    try {
        const res = await Activity.getJsonConfig(_params);

        return JSON.parse(res);
    } catch (error) {
        console.error('自定义json请求失败', error);
        return {};
    }
}

/**
 * json配置闭包
 * @param _params 根据参数执行如下：1. true: 请求所有配置; 2. object: 根据参数请求配置; 3. undefined: 不请求配置
 * @returns 
 */
export function useJsonConfig(_params?: IJsonParams | boolean) {

    (async function () {
        try {
            if (typeof _params === "boolean" && _params) {
                // 请求所有配置
                Object.assign(globalJsonConfig, await getJsonConfig());
            } else if (typeof _params === "object") {
                // 根据参数请求配置
                Object.assign(globalJsonConfig, await getJsonConfig(_params));
            }
        } catch (error) {
            console.error('自定义json请求失败', error);
            return;
        }
    })()


    /**
     * 根据关键字获取json配置
     * @param key 关键字
     */
    async function getJson(key?: string | Array<string>) {
        if (!key) {
            return globalJsonConfig;
        } else if (typeof key === "string") {
            if (Object.prototype.hasOwnProperty.call(globalJsonConfig, key)) {
                return globalJsonConfig[key];
            }

            const res = getJsonConfig({ keys: [key] });

            Object.assign(globalJsonConfig, res);

            return res[key];
        } else {
            const _temJson: Record<string, any> = {};
            const reqList: Array<string> = [];
            key.forEach(val => {
                if (Object.prototype.hasOwnProperty.call(globalJsonConfig, val)) {
                    _temJson[val] = globalJsonConfig[val];
                } else {
                    reqList.push(val);
                }
            })

            if (reqList.length > 0) {
                const res = getJsonConfig({ keys: reqList });
                Object.assign(globalJsonConfig, res);
                Object.assign(_temJson, res);
            }

            return _temJson;
        }
    }

    /**
     * 清空json配置
     */
    function clearJson() {
        Object.keys(globalJsonConfig).forEach(key => {
            delete globalJsonConfig[key];
        });
    }

    /**
     * 全局安装json配置
     * @param app vue实例
     */
    function installJsonConfig(app: App) {
        app.config.globalProperties.$jsonConfig = computed(() => globalJsonConfig);
    }

    return {
        jsonConfig: computed(() => globalJsonConfig),
        getJson,
        clearJson,
        installJsonConfig
    }
}
