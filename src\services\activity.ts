import xhr from './xhr'
import { useEnvConfig } from '@/hook'

const { frontendConfigUrl, jsonConfigUrl } = useEnvConfig()

interface IFrontendConfig {
  activityType: string
  chooseLang: string[]
  defaultLang: string
  endTime: string
  startTime: string
  publishStatus: string
  name: string
  groupList: string
}

export interface IJsonParams {
  keys?: Array<string>
  groups?: Array<string>
}

class Activity {
  /** 获取kv配置 */
  getFrontendConfig() {
    return xhr.get<IFrontendConfig>(frontendConfigUrl)
  }

  /**
   * 获取json参数
   * @param _params 参数
   * @returns
   */
  getJsonConfig(_params?: IJsonParams) {
    const data = {
      keys: [],
      groups: []
    }

    _params && Object.assign(data, _params)
    return xhr.post<string>(jsonConfigUrl, {
      data
    })
  }
}

export default new Activity()
