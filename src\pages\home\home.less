.home {
  width: 750px;
  .banner-swipe {
    display: block;
    width: 750px;
    aspect-ratio: 750 / 1276;
    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }
  .menu-box {
    padding: 80px 32px 0;
    .menu-item {
      position: relative;
      width: 687px;
      height: 176px;
      border-radius: 20px;
      overflow: hidden;
      margin-bottom: 24px;
      &:last-child {
        margin-bottom: 0;
      }
      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
      .title {
        pointer-events: none; /* 禁止鼠标交互 */
        user-select: none;    /* 禁止文字选择 */
        position: absolute;
        top: 0;
        left: 32px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        height: 100%;
        z-index: 1;
        p {
          &:nth-child(1) {
            font-size: 40px;
            font-weight: 600;
            color: #ffffff;
            padding-bottom: 8px;
          }
          &:nth-child(2) {
            font-size: 24px;
            font-weight: 400;
            color: #ffffff;
          }
        }
      }
      .tag {
        position: absolute;
        top: 0;
        left: 0;
        width: 64px;
        height: 48px;
        background: rgba(38, 39, 41, 0.2);
        border-radius: 0 0px 32px 0px;
        font-size: 24px;
        color: #ffffff;
        display: flex;
        align-items: center;
        justify-content: center;
      }
      :deep(.swiper-slide) {
        border-radius: 21pw;
        overflow: hidden;
      }
      :deep(.swiper-custom-pagination) {
        position: absolute;
        z-index: 10;
        bottom: 8px;
        left: 0;
        right: 0;
        margin: auto;
        text-align: center;
        z-index: 10;
        .swiper-pagination-bullet {
          width: 8px;
          height: 8px;
          background: black;
        }
        .swiper-pagination-bullet-active {
          width: 16px;
          height: 8px;
          background: #ffffff;
          border-radius: 8px;
        }
      }
    }
  }
  .flexMenu {
    display: flex;
    position: fixed;
    width: 100%;
    height: 96px;
    z-index: 100;
    background: white;
    border-bottom: 1px solid #e0e0e0;
    overflow-x: auto;
    white-space: nowrap;
    span {
      cursor: pointer;
      display: inline-flex;
      padding: 0 40px;
      font-size: 28px;
      color: rgba(38, 39, 41, 0.8);
      text-align: center;
      align-items: center;
      justify-content: center;
      border: 1px solid #e0e0e0;
      &.active {
        color: white;
        background: #00a7ff;
      }
    }
  }
  .cont {
    padding: 80px 0;
    .cont-box {
      width: calc(100% - 64px);
      margin: 0 auto;
      .title {
        font-size: 40px;
        color: #2f85ff;
        margin-bottom: 50px;
      }
    }
    &.cont1 {
      .cont-box {
        .txt-box {
          position: relative;
          background: url('@/assets/imgs/mobile/cont1-bg.png') no-repeat top center;
          background-size: 100% auto;
        }
        .pic {
          position: absolute;
          width: 80px;
          height: 80px;
          top: -80px;
          right: 32px;
        }
        .tab-box {
          .tab-list {
            display: flex;
            align-items: center;
            &:first-child {
              .tab-item {
                &:first-child {
                  margin-right: 32px;
                }
              }
            }
            &:nth-of-type(2) {
              margin-top: 32px;
              margin-right: 70px;
              justify-content: right;
            }
            &:nth-of-type(3) {
              margin-top: 32px;
              justify-content: right;
            }
            .tab-item {
              display: table;
              font-size: 24px;
              color: #2f85ff;
              padding: 24px 36px;
              border: 1px solid #2f85ff;
              border-radius: 200px 60px 0px 200px;
              &.active {
                background: #2f85ff;
                color: white;
              }
            }
          }
        }
        .tab-cont {
          .q {
            display: flex;
            align-items: center;
            img {
              width: 268px;
              height: 212px;
            }
            p {
              flex: 1;
              font-weight: 400;
              font-size: 24px;
              color: rgba(38, 39, 41, 0.8);
              line-height: 36px;
            }
          }
          .a {
            width: 686px;
            background: #ffffff;
            box-shadow: 0px 8px 24px 0px rgba(0, 0, 0, 0.08);
            border-radius: 0px 32px 32px 32px;
            padding: 32px;
            font-size: 28px;
            color: rgba(38, 39, 41, 0.6);
            line-height: 42px;
            margin-top: -20px;
          }
        }
      }
    }
    &.cont2 {
      background: #f4f9ff;
      padding-bottom: 26px;
      .cont-box {
        .title-small {
          padding-bottom: 24px;
          display: flex;
          flex-wrap: wrap;
          align-items: center;
          line-height: 1.4;
          p {
            font-size: 28px;
            color: #262729;
            margin-right: 24px;
            em {
              color: #2f85ff;
              padding-right: 16px;
            }
          }
          span {
            display: block;
            font-size: 24px;
            color: rgba(38, 39, 41, 0.6);
          }
        }
        .cont2-img {
          margin-bottom: 48px;
        }
        .table-box {
          position: relative;
          .btnBox {
            position: absolute;
            left: 0;
            right: 0;
            top: 130px;
            .btn {
              display: flex;
              align-items: center;
              justify-content: space-around;
              span {
                width: 192px;
                height: 54px;
                display: block;
              }
              &.text{
                margin-top: 10px;
                span{
                  height:30px;
                }
              }
              a{
                width: 100%;
                height: 100%;
                display: block;
              }
            }
          }
        }
      }
    }
    &.cont3 {
      padding: 0;
      background: #f4f9ff;
      padding-bottom: 80px;
      .cont-box{
        width: 100%;
      }
      .title-small {
        padding: 0 32px 24px;
        line-height: 1.4;
        p {
          font-size: 28px;
          color: #262729;
          span{
            display: inline-block;
            padding-left: 24px;
          }
          em {
            color: #2f85ff;
            padding-right: 16px;
          }
        }
        span {
          display: block;
          font-size: 24px;
          color: rgba(38, 39, 41, 0.6);
        }
      }
      .plan {
        width: 100%;
        padding-bottom: 58px;
        display: flex;
        gap: 16px;
        overflow-x: auto;
        overflow-y: hidden;
        width: 100%;
        padding-left: 32px;
        padding-right: 32px;
        scrollbar-width: none; 
        -ms-overflow-style: none;
        &::-webkit-scrollbar {
          display: none; 
        }
        .item {
          width: 670px;
          flex-shrink: 0;
          border-radius: 32px;
          box-shadow: 0px 8px 32px 0px rgba(0, 0, 0, 0.08);
          overflow: hidden;
          background: white;
        }
        .header {
          min-height: 96px;
          display: flex;
          align-items: center;
          padding:0 32px;
          background: linear-gradient(to right, #007bff, #00e676);
          >.title {
            font-size: 28px;
            color: #fff;
            font-weight: bold;
            margin: 0;
          }
        }
        .content {
          .banner {
            height: 202px;
            width: 100%;
          }
          .info {
            margin-top: 36px;
            display: flex;
            gap: 30px;
            .row {
              &:first-child {
                .details {
                  strong {
                    color: #d5197f;
                  }
                }
              }
              width: (1/3)*100%;
              display: flex;
              flex-direction: column;
              justify-content: space-between;
              font-size: 12px;
              color: #666;
              span {
                font-size: 28px;
                color: rgba(38, 39, 41, 0.6);
                text-align: center;
              }
              .details {
                display: flex;
                align-items: center;
                justify-content: center;
                margin-top: 30px;
                strong {
                  font-family: Saira ExtraCondensed, Saira ExtraCondensed;
                  font-weight: bold;
                  font-size: 40px;
                  color: #262729;
                  i {
                    font-size: 28px;
                    font-style: normal;
                  }
                }

                button {
                  width: 110px;
                  height: 56px;
                  background: #f5f5f5;
                  border-radius: 48px;
                  border: 2px solid #00a7ff;
                  background: #f5f5f5;
                  font-size: 28px;
                  color: #00a7ff;
                }
              }
            }
          }
        }
        .discounts {
          padding: 32px;
          p {
            width: 100%;
            overflow: hidden;
            font-size: 28px;
            color: #666;
            margin-bottom: 36px;
            position: relative;
            &::after {
              position: absolute;
              top: 0;
              right: 0;
              bottom: 0;
              margin: auto;
              height: 1px;
              content: '';
              display: inline-block;
              width: 488px;
              border-top: 1px solid rgba(38, 39, 41, 0.2);
            }
          }
          ul {
            padding: 0;
            li {
              &:first-child {
                font-size: 32px;
                color: #000000;
                i {
                  font-style: normal;
                  color: #eb6ea5;
                }
              }
              &:last-child {
                margin-bottom: 0;
              }
              display: flex;
              align-items: center;
              margin-bottom: 32px;
              font-size: 28px;
              color: rgba(38, 39, 41, 0.8);
              img {
                width: 64px;
                height: 64px;
                margin-right: 24px;
              }
            }
          }
        }
        .footer {
          text-align: center;
          padding-bottom: 32px;
          button {
            width: 622px;
            height: 92px;
            border-radius: 80px;
            border: none;
            background: #00a7ff;
            cursor: pointer;
            font-weight: 400;
            font-size: 32px;
            color: #ffffff;
          }
        }
      }
      .thali {
        width: 686px;
        margin: 0 auto;
        background: white;
        padding-bottom: 50px;
        border-radius: 20px;
        .tit {
          display: flex;
          align-items: center;
          width: 686px;
          height: 96px;
          background: linear-gradient(90deg, #4dc610 0%, #b8e823 100%);
          font-weight: bold;
          font-size: 28px;
          color: #ffffff;
          padding: 0 54px;
          border-radius: 20px 20px 0 0;
        }
        > img {
          width: 100%;
        }
        .price-box {
          padding: 36px 0;
          width: calc(100% - 64px);
          margin: 0 auto;
          display: flex;
          align-items: center;
          justify-content: space-between;
          border-bottom: 2px solid rgba(38, 39, 41, 0.2);
          .left {
            text-align: center;
            i {
              display: block;
              font-style: normal;
              font-size: 28px;
              color: rgba(38, 39, 41, 0.6);
            }
            .price {
              span {
                letter-spacing: 0;
                font-size: 56px;
                color: #d5197f;
                font-weight: 800;
                font-family: 'SairaExtraCondensed-Bold';
                &.mini {
                  font-size: 32px;
                }
              }
            }
            .old-price {
              font-size: 28px;
              color: rgba(38, 39, 41, 0.6);
              font-style: normal;
              text-decoration-line: line-through;
            }
          }
          .right {
            text-align: center;
            p {
              font-size: 28px;
              color: rgba(38, 39, 41, 0.6);
            }
            span {
              width: 110px;
              height: 56px;
              line-height: 56px;
              text-align: center;
              display: block;
              font-size: 28px;
              color: #2f85ff;
              border: 1px solid #2f85ff;
              border-radius: 48px;
              background: #f5f5f5;
              margin: 16px auto 0;
            }
          }
        }
        .desc {
          padding: 32px;
          .oLi {
            display: flex;
            align-items: flex-start;
            margin-bottom: 32px;
            &:last-child {
              margin-bottom: 0;
            }
            img {
              width: 64px;
              height: 64px;
              margin-right: 10px;
              transform: translateY(-12px);
            }
            i {
              font-style: normal;
              color: #eb6ea5;
            }
            span {
              font-size: 28px;
              color: rgba(38, 39, 41, 0.8);
            }
            b {
              font-size: 28px;
              color: #262729;
            }
          }
        }
        button {
          width: 622px;
          height: 92px;
          line-height: 92px;
          background: #2f85ff;
          border-radius: 72px 72px 72px 72px;
          font-size: 32px;
          color: #ffffff;
          text-align: center;
          margin: 0 auto;
          display: block;
        }
      }
    }
    &.cont4 {
      background: #fff;
      .small-title {
        display: flex;
        align-items: center;
        font-size: 30px;
        padding-bottom: 24px;
        em {
          font-size: 30px;
          color: #2f85ff;
          padding-right: 10px;
        }
        i {
          font-family: DINPro, DINPro;
          font-weight: bold;
          font-size: 40px;
          color: #00a7ff;
          font-size: 40px;
          padding-right: 8px;
        }
        &.title2 {
          i {
            color: #1ad586;
          }
        }
      }
      .step {
        &.step2 {
          .item {
            .desc {
              background: url('@/assets/imgs/mobile/bg1.png') no-repeat center center;
              background-size: 100% 100%;
              span {
                font-weight: 500;
                font-size: 28px;
                color: rgba(38, 39, 41, 0.8);
                i {
                  color: #eb6ea5;
                  font-style: normal;
                }
              }
            }
          }
        }
        .name {
          text-align: center;
          font-size: 28px;
          color: rgba(38, 39, 41, 0.8);
          padding-bottom: 24px;
        }
        .item {
          display: flex;
          align-items: center;
          .icon {
            width: 104px;
            height: 104px;
          }
          .desc {
            min-height: 140px;
            flex: 1;
            padding: 20px 24px;
            background: url('@/assets/imgs/mobile/bg2.png') no-repeat center center;
            background-size: 100% 100%;
            display: flex;
            flex-direction: column;
            justify-content: center;
            span {
              display: block;
              font-size: 28px;
              color: rgba(38, 39, 41, 0.8);
              padding-bottom: 6px;
            }
            p {
              font-weight: 400;
              font-size: 20px;
              color: rgba(38, 39, 41, 0.6);
            }
          }
        }
        .next {
          width: 28px;
          height: 28px;
          margin: 24px auto;
        }
      }
      .table {
        padding: 24px 0 48px;
        .custom-table {
          width: 100%;
          border-collapse: collapse;
          border-radius: 24px;
          overflow: hidden;
          background-color: #f9f9f9;
        }
        .custom-table th,
        .custom-table td {
          height: 108px;
          border: 1px solid #ddd;
          padding: 8px;
          text-align: center;
        }
        .custom-table th {
          background-color: #e3e3e3;
        }
        .custom-table tr:first-child td {
          grid-column: span 2;
          background-color: #00a7ff;
          font-size: 28px;
          color: #ffffff;
        }
        .row2 {
          background: #f4f9ff;
          color: rgba(38, 39, 41, 0.8);
        }
        .row3 {
          font-size: 20px;
          color: rgba(38, 39, 41, 0.8);
          background: #ffffff;
          i {
            color: #eb6ea5;
            font-style: normal;
          }
        }
      }
      .code {
        margin-top: 24px;
        width: 686px;
        background: rgba(26, 213, 134, 0.05);
        border-radius: 32px 32px 32px 32px;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 28px;
        .code-box {
          width: 178px;
          height: 178px;
          border-radius: 24px;
          margin-right: 40px;
          overflow: hidden;
          img {
            width: 100%;
            height: 100%;
          }
        }
        .share-nr {
          flex: 1;
          p {
            font-weight: 500;
            font-size: 40px;
            color: rgba(38, 39, 41, 0.8);
            padding-bottom: 12px;
          }
          span {
            font-weight: 400;
            font-size: 28px;
            color: rgba(38, 39, 41, 0.6);
          }
        }
      }
      .rule {
        padding-top: 26px;
        font-weight: 400;
        font-size: 28px;
        color: rgba(38, 39, 41, 0.6);
        text-align: center;
        font-style: normal;
        text-decoration-line: underline;
      }
    }
    &.cont5 {
      background: #f4f9ff;
      .list {
        :deep(.question) {
          &:nth-of-type(2n) {
            .q {
              background: #1ad586;
              .triangle {
                border-top: 20px solid #1ad586;
              }
            }
          }
        }
      }
    }
  }
}
