declare global {
	interface Window {
    VConsole: any
    load: (options: {
      type: 'js' | 'css'
      src: string
      module?: boolean
      crossorigin?: string
      [key: string]: any
    }) => Promise<void>
		__WUJIE_RAW_WINDOW__?: any
        __vite_env__?: Record<string,any>
        __POWERED_BY_WUJIE__?: any
        $wujie?: {
            props?: any
            shadowRoot: any
            bus?: {
              $on?: any
              $emit?: any
            }
        }
        OmniApiEnv?: any
        OmniApi?: any
		// mylink 项目特有属性
		webkit: {
			messageHandlers: any;
		};
		HkAndroid: any;
	}
}
export {}