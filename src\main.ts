import { createApp, App as AppVue, ComponentPublicInstance } from 'vue'
import App from './App.vue'
import router from './router'
import pinia, { useAppStore } from './store'
import { installDialog, useOmniapi, useEnvConfig, useUtils, useLoader, useJsonConfig } from '@/hook'
import i18n from './i18n'
import { install } from '@/assets/imgs'

const { loadJs } = useLoader()
const {OMNI_API_SDK_ENV, OMNI_PARTNER_TOKEN, RUN_ENV, MYLINK_SDK_URL, LOG_ENV, ArmasPid, ACTIVITY_NAME } = useEnvConfig()
// const { installJsonConfig } = useJsonConfig(true)
useOmniapi().sdkInit(OMNI_API_SDK_ENV, OMNI_PARTNER_TOKEN)
;(async () => {
  if (RUN_ENV != 'develop') {
    const { isOK, detail } = await useOmniapi().pageInit()
    console.log('pageInit isOK:', isOK, detail)
  }
  const { utils } = useUtils()
  const app = createApp(App)
  install(app)
  app.config.errorHandler = (err: unknown, instance: ComponentPublicInstance | null, info: string) => {
    console.error('app-catch-error', err, instance, info)
  }
  function startDebug() {
    if (RUN_ENV !== 'production' && utils.getHashQueryString('debug')) {
      loadJs('https://cdn.mylink.hk.chinamobile.com/via/resource/activity/2025-05-15/7f246b20769e4ac8871494a89fd2cef2.js').then((res) => {
        let vConsole = new window.VConsole()
      })
    }
  }
  startDebug()

  app.use(pinia).use(i18n)
  const closeLoadingToast = showLoadingToast({
    duration: 0
  })
  const appStore = useAppStore()
  
  /**
   * 非无界嵌入，属于mylink环境
   * 加载mylinkSDK
   */
  if (!appStore.isDecEnv && RUN_ENV != 'develop') {
    await window
      .load({
        type: 'js',
        src: MYLINK_SDK_URL,
        'close-arms': 'true',
        id: 'mylinkSdk',
        'ali-log-uid': ACTIVITY_NAME,
        'ali-arms-pid': ArmasPid,
        env: LOG_ENV
      })
      .catch(() => {})
  }
  app.use(router)
  installDialog(app)
  // installJsonConfig(app)
  app.mount('#app')
  closeLoadingToast.close()
})()
