
import type { EnvConfig } from '../../../config/useEnvConfig'
export type NODE_ENV_TYPE = 'production' | 'development'

const envConfig: EnvConfig['production'] & {
  NODE_ENV: NODE_ENV_TYPE
  isProd: boolean
} = {} as EnvConfig['production'] & {
  NODE_ENV: NODE_ENV_TYPE
  isProd: boolean
}
const suffix = 'VITE_APP_'
const config = window.__vite_env__ as EnvConfig['production']

const keys = Object.keys(config)
for (let i = 0; i < keys.length; ++i) {
  let key = keys[i]
  if (key.startsWith(suffix)) {
    key = key.substring(suffix.length)
  }
  envConfig[key] = config[keys[i]]
}
envConfig.isProd = envConfig.NODE_ENV === 'production'
export function useEnvConfig() {
  return envConfig
}
