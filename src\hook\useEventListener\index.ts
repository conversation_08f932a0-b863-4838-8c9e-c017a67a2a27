export interface TypeEventListener {
    addEventListener: (
        type: string,
        handler: EventListenerOrEventListenerObject,
        options?: boolean | AddEventListenerOptions
    ) => void
    removeEventListener: (
        type: string,
        handler: EventListenerOrEventListenerObject,
        options?: boolean | AddEventListenerOptions
    ) => void
    [key: string]: any
}

let supportsPassive = false

try {
    const opts = {}
    Object.defineProperty(opts, 'passive', {
        get() {
            supportsPassive = true
        }
    })
    window.addEventListener('test-passive', null as any, opts)
} catch (e) {}

export function useEventListener(
    scope: TypeEventListener,
    type: string | string[],
    handler: EventListenerOrEventListenerObject,
    options?: boolean | AddEventListenerOptions
) {
    if (options && typeof options !== 'boolean') {
        const { passive = false, capture = false } = options
        options = supportsPassive ? { capture, passive } : capture
    }
    if (typeof type === 'string') {
        scope.addEventListener(type, handler, options)
        return () => {
            scope.removeEventListener(type, handler, options)
        }
    }
    const resultList: Array<() => void> = []
    type.forEach((item) => {
        scope.addEventListener(item, handler, options)
        resultList.push(() => {
            scope.removeEventListener(item, handler, options)
        })
    })
    return () => {
        resultList.forEach((fn) => fn())
    }
}
