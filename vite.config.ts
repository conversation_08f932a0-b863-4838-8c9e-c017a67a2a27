import { defineConfig, Plugin } from 'vite'
import vue from '@vitejs/plugin-vue'
import vueJsx from '@vitejs/plugin-vue-jsx'
import { useAlias } from './config/useAlias'
import { useEnvConfig } from './config/useEnvConfig'
import { createHtmlPlugin } from 'vite-plugin-html'
import externalGlobals from 'rollup-plugin-external-globals'
import { resolve } from 'path'
import postcssPxToViewport from 'postcss-px-to-viewport-8-plugin'
import Components from 'unplugin-vue-components/vite'
import { VantResolver } from '@vant/auto-import-resolver'
import AutoImport from 'unplugin-auto-import/vite'
import legacy from '@vitejs/plugin-legacy'
const envConfig = useEnvConfig()

const alias = [...useAlias()]

const injectData = {
  __vite_env__: JSON.stringify(envConfig),
  injectScriptAfterHead: '',
  injectScriptBeforeBody: '',
  injectScriptAfterBody: '',
  injectScriptAfterVue: ''
}

if (envConfig.RUN_ENV !== 'develop' && envConfig.RUN_ENV !== 'uat') {
  Object.assign(injectData, {
    injectScriptBeforeBody: ``,
    injectScriptAfterVue: ``,
    injectScriptAfterHead: ``
  })
} else {
  Object.assign(injectData, {
    // injectScriptBeforeBody: reportURI.uat
  })
}

export default defineConfig({
  base: envConfig.BASE,
  plugins: [
    vue(),
    //解决支付宝低版本白屏问题
    legacy({
      targets: ['Chrome 69'],
      additionalLegacyPolyfills: ['regenerator-runtime/runtime'],
      modernPolyfills: true
    }),
    vueJsx(),
    AutoImport({
      resolvers: [VantResolver()]
    }),
    Components({
      resolvers: [VantResolver()]
    }),
    createHtmlPlugin({
      minify: true,
      template: 'index.html',
      inject: {
        data: injectData
      }
    }),
    {
      name: 'custom-html-transform',
      transformIndexHtml(html) {
        return html.replace(
          /<script type="module" crossorigin src="(.*?)"><\/script>/g,
          '<script type="module" crossorigin="use-credentials" src="$1"></script>'
        )
      }
    }
  ],
  resolve: {
    alias
  },
  css: {
    postcss: {
      plugins: [
        {
          postcssPlugin: 'internal:charset-removal',
          AtRule: {
            charset: (atRule) => {
              if (atRule.name === 'charset') {
                atRule.remove()
              }
            }
          }
        },
        postcssPxToViewport({
          landscape: false,
          mediaQuery: true,
          unitToConvert: 'px',
          viewportWidth(file) {
            return file.indexOf('vant') !== -1 ? 375 : 750
          },
          propList: ['*'],
          // 指定不参与转换的类名
          selectorBlackList: ['v--web']
        }),
        postcssPxToViewport({
          landscape: false,
          mediaQuery: true,
          unitToConvert: 'pw',
          viewportWidth: 1920,
          propList: ['*']
        })
      ]
    },
    preprocessorOptions: {
      less: {
        javascriptEnabled: true,
        additionalData: `
          @import "${resolve(__dirname, 'src/styles/less/g-mixin.less')}";
        `
      }
    }
  },
  server: {
    port: 3003,
    host: '0.0.0.0'
  },
  build: {
    manifest: false,
    sourcemap: true,
    // CDN引入的话，使用这个插件做配置。但是这里引入的文件要是遵循umd格式的，此项只会在打包的文件中使用，未打包状态下的dev模式中不会走这里
    rollupOptions: {
      output: {
        manualChunks(id) {
          if (id.includes('node_modules') && !id.includes('.pnpm')) {
            return id.toString().split('node_modules/')[1].split('/')[0].toString()
          }
        },
        chunkFileNames: 'assets/js/[name]-[hash].js',
        entryFileNames: 'assets/js/[name]-[hash].js',
        // assetFileNames: 'assets/static/[ext]/[name]-[hash].[ext]',
        assetFileNames: 'assets/static/[name]-[hash].[ext]'
      },
      // eslint-disable-next-line prettier/prettier
      external: ['@via/mylink-sdk', 'regenerator-runtime/runtime'],
      plugins: [
        externalGlobals({
          '@via/mylink-sdk': 'mylinkSdk'
        })
      ]
    }
  }
})
