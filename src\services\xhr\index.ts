import axios from './axiosConfig.js'
import { useStorage, useEnvConfig, useJwt, useLang } from '@/hook'
import qs from 'qs'
import { useUtils, useOmniapi } from '@/hook'
import loading from './loading'
import { Method, AxiosResponse, AxiosRequestConfig, AxiosProxyConfig } from 'axios'
import { md5 } from 'md5js'
import { encode, decode } from './JtpAes'
import { useAppStore } from '@/store'
const { state } = useLang()

let cacheJwt = ''
let cacheToken = ''

let omniSdkPageInit = false
const { utils } = useUtils()
const { API_HOST } = useEnvConfig()
const storage = useStorage()
export function setJwt(jwt: string) {
  let success = false
  const { leftTime } = useJwt(jwt)
  if (leftTime > 0) {
    cacheJwt = jwt
    let key = 'login-jwt'
    storage.save(key, jwt, leftTime)
    success = true
  } else {
    cacheJwt = ''
  }
  return success
}

/**
 *
 * @param token
 * @param exp 过期时间
 * @returns
 */
export function setToken(token: string, exp?: number) {
  let success = false
  try {
    success = true
    cacheToken = token
    let key = 'login-token'
    if (exp) {
      storage.save(key, token, exp)
    } else {
      storage.save(key, token)
    }
    return success
  } catch (error) {
    cacheToken = ''
    console.error('设置token错误:', error)
    return success
  }
}

export type Options = AxiosRequestConfig & {
  useAuth?: boolean
  refresh?: boolean
  loading?: boolean
  hideCommToast?: boolean
  useAse?: boolean
  isOmniapi?: boolean
  timeout?: number
}
export type ReturnData<T = any> = {
  code: number
  msg: string
  data: T
}

const cacheData: Record<string, any> = {}
const cacheDataKeys: string[] = []
const requestList: Record<string, { fnList: any }> = {}
function getKVStr(obj: Record<string, any>) {
  return Object.entries(obj)
    .map(([k, v]) => `${k}=${JSON.stringify(v)}`)
    .sort()
    .join('&')
}
function getMD5(method: Method, url: string, params: Record<string, any>, data: Record<string, any>, headers: Record<string, any>) {
  const str = [method, url, getKVStr(params), getKVStr(data), getKVStr(headers)].join('_')
  return md5(encodeURI(str), 16)
}
async function preDeal<T = any>(md5Str: string, fn: Promise<AxiosResponse<ReturnData<T>>>, isOmniapi = false) {
  return fn
    .then((res) => {
      cacheData[md5Str] = res
      cacheDataKeys.push(md5Str)
      // 只缓存20条数据
      if (cacheDataKeys.length > 20) {
        const key = cacheDataKeys.shift()
        if (key) {
          Reflect.deleteProperty(cacheData, key)
        }
      }
      if (requestList[md5Str]) {
        requestList[md5Str].fnList.forEach((resolve: any) => {
          resolve(res)
        })
        Reflect.deleteProperty(requestList, md5Str)
      }
      return isOmniapi ? (res as unknown as ReturnData<T>) : res.data
    })
    .catch((e) => {
      Reflect.deleteProperty(requestList, md5Str)
      throw e
    })
}

let responseInterceptor: (<T>(res: ReturnData<T>) => Promise<ReturnData<T>>) | undefined

const omniapiXhr = (options: Options) => {
  const headers: Map<string, string> = new Map()
  for (var key in options.headers) {
    if (options.headers.hasOwnProperty(key)) {
      headers.set(key, options.headers[key])
    }
  }
  if (options.method == 'get') {
    return useOmniapi().jsonGetXhr(API_HOST + options.url + `?${qs.stringify(options.params)}`, headers, options.timeout)
  } else {
    if (headers.get('Content-Type')?.includes('application/x-www-form-urlencoded')) {
      return useOmniapi().formPostXhr(API_HOST + options.url, options.data, headers, options.timeout)
    } else {
      return useOmniapi().jsonPostXhr(API_HOST + options.url, options.data, headers, options.timeout)
    }
  }
}

async function realRequest<T = any>(method: Method, url: string, options: Options) {
  const opt = Object.assign({ refresh: true, data: {}, params: {}, headers: {} }, options)
  console.groupCollapsed('%c api请求发起', 'color: #ffffff;background-color: #8f4b2e;padding: 5px', url)
  console.log('请求方式：', method)
  console.log('请求链接：', url)
  console.log('请求参数：', opt)
  opt.headers.lang = window.sessionStorage.getItem('lang') || 'tc'
  opt.headers.language = window.sessionStorage.getItem('lang') || 'tc'
  const isOmniapi = options.isOmniapi
  console.groupEnd()
  if (opt.useAse) {
    opt.data = encode(opt.data)
  }
  const md5Str = getMD5(method, url, opt.params, opt.data, opt.headers || {})
  let fn: Promise<AxiosResponse<ReturnData<T>>>
  if (requestList[md5Str]) {
    // 已经有相同的接口发起了，等待结果返回
    console.log('有相同接口发起，整合成一个接口')
    fn = new Promise<AxiosResponse<ReturnData<T>>>((resolve) => {
      requestList[md5Str].fnList.push(resolve)
    })
  } else {
    requestList[md5Str] = {
      fnList: []
    }
    if (!opt?.refresh && cacheData[md5Str]) {
      console.log('从缓存获取数据')
      fn = new Promise<AxiosResponse<ReturnData<T>>>((resolve) => {
        resolve(cacheData[md5Str])
      })
    } else {
      let key = utils.onlyKeyCreator()
      if (opt.loading) {
        // 含loading的请求列表
        loading.pushRequest(key, url)
      }
      if (!isOmniapi) {
        fn = axios({
          ...options,
          url,
          method,
          params: opt.params,
          data: opt.data,
          headers: Object.assign(
            {},
            opt.headers,
            opt?.useAuth && {
              Authorization: cacheToken
                ? cacheToken
                : cacheJwt
                ? `Bear ${cacheJwt}`
                : storage.load('login-jwt')
                ? storage.load('login-jwt').v
                : ''
            }
          )
        })
          .then((res) => {
            if (opt.loading) {
              loading.finishRequest(key)
            }

            try {
              if (opt.useAse && res.data.data) {
                res.data.data = decode(res.data.data) || res.data.data
              }
            } catch (error) {
              console.log(error)
            }
            return res
          })
          .catch((err) => {
            // 请求失败清空请求列表
            if (opt.loading) {
              loading.finishRequest(key)
            }
            !opt.hideCommToast && showToast(err?.response?.data.msg || '網絡異常,請重試')
            // useAppStore().toastMessage = err?.response?.data.msg || '网络异常,请重试'
            throw err
          })
      } else {
        if (!omniSdkPageInit) {
          const { isOK, detail } = await useOmniapi().pageInit()
          console.log('isOk', isOK, detail)
          omniSdkPageInit = isOK
        }
        fn = omniapiXhr({
          ...options,
          url,
          method,
          params: opt.params,
          data: opt.data,
          headers: Object.assign(
            {},
            opt.headers,
            opt?.useAuth && {
              Authorization: cacheToken
                ? cacheToken
                : cacheJwt
                ? `Bear ${cacheJwt}`
                : storage.load('login-jwt')
                ? storage.load('login-jwt').v
                : ''
            }
          )
        })
          .then((res) => {
            if (opt.loading) {
              loading.finishRequest(key)
            }

            try {
              if (opt.useAse && res.data.data) {
                res.data.data = decode(res.data.data) || res.data.data
              }
            } catch (error) {
              console.log(error)
            }
            return res
          })
          .catch((err) => {
            // 请求失败清空请求列表
            if (opt.loading) {
              loading.finishRequest(key)
            }
            !opt.hideCommToast && showToast(err?.response?.data.msg || '網絡異常,請重試')
            // useAppStore().toastMessage = err?.response?.data.msg || '网络异常,请重试'
            throw err
          })
      }
    }
  }
  return preDeal<T>(md5Str, fn, isOmniapi)
}

async function request<T = any>(method: Method, url: string, options: Options): Promise<T> {
  return realRequest<T>(method, url, options).then(async (res) => {
    console.groupCollapsed('%c api请求结束', 'color: #ffffff;background-color: #5c7a29;padding: 5px', url)
    console.log('请求方式：', method)
    console.log('请求链接：', url)
    console.log('请求结果：', res)
    console.groupEnd()
    if (responseInterceptor) {
      res = await responseInterceptor<T>(res)
    }
    if (+res.code !== 0) {
      useAppStore().isLoading = false
      !options.hideCommToast && showToast(res.msg || '网络异常,请重试')
      // useAppStore().toastMessage = res.msg || '网络异常,请重试'
      throw res
    }

    return res.data
  })
}

export function registerResponseInterceptor(fn?: <T>(res: ReturnData<T>) => Promise<ReturnData<T>>) {
  responseInterceptor = fn
}

class xhr {
  get<T = any>(url: string, options: Options = {}) {
    return request<T>('get', url, options)
  }
  omniapiGet<T = any>(url: string, options: Options = {}) {
    return request<T>('get', url, Object.assign({ isOmniapi: true }, options))
  }
  authGet<T = any>(url: string, options: Options = {}) {
    return request<T>('get', url, Object.assign({ useAuth: true }, options))
  }
  omniapiAuthGet<T = any>(url: string, options: Options = {}) {
    return request<T>('get', url, Object.assign({ useAuth: true, isOmniapi: true }, options))
  }
  post<T = any>(url: string, options: Options = {}) {
    return request<T>('post', url, options)
  }
  omniapiPost<T = any>(url: string, options: Options = {}) {
    return request<T>('post', url, Object.assign({ isOmniapi: true }, options))
  }
  omniapiAuthPost<T = any>(url: string, options: Options = {}) {
    return request<T>('post', url, Object.assign({ useAuth: true, isOmniapi: true }, options))
  }
  authPost<T = any>(url: string, options: Options = {}) {
    return request<T>('post', url, Object.assign({ useAuth: true }, options))
  }
  authRequest<T = any>(method: Method, url: string, options: Options = {}) {
    return request<T>(method, url, Object.assign({ useAuth: true }, options))
  }
  request<T = any>(method: Method, url: string, options: Options = {}) {
    return request<T>(method, url, options)
  }
  realRequest<T = any>(method: Method, url: string, options: Options = {}) {
    return realRequest<T>(method, url, options)
  }
}

export default new xhr()
