import sc from './sc'
import en from './en'
import tc from './tc'
import type { App } from 'vue'

export type CustomLang = typeof tc

import { useLang } from '@/hook'

const message = {
  sc,
  tc,
  en
}

useLang().setMessages(message)

export default {
  install(app: App) {
    app.config.globalProperties.$lang = useLang().state
    app.config.globalProperties.$langValue = useLang().lang
    app.config.globalProperties.$langFormat = useLang().format
  }
}

declare module '@/hook' {
  namespace UseLang {
    interface Lang extends CustomLang {}
  }
}
