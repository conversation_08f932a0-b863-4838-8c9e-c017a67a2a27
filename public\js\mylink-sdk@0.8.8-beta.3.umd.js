/*!
 * mylink-sdk v0.8.8-beta.3
 * 穢 2021-~ via
 */
!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports,require("crypto")):"function"==typeof define&&define.amd?define(["exports","crypto"],e):e((t="undefined"!=typeof globalThis?globalThis:t||self).mylinkSdk={},t.require$$0)}(this,(function(t,e){"use strict";function n(t){return t&&"object"==typeof t&&"default"in t?t:{default:t}}var i,o=n(e);!function(t){t.prod="prod",t.gray="gray",t.daily="daily",t.local="local"}(i||(i={}));var r=Object.freeze({__proto__:null,get AliLogEnvironment(){return i}}),a=function(){return a=Object.assign||function(t){for(var e,n=1,i=arguments.length;n<i;n++)for(var o in e=arguments[n])Object.prototype.hasOwnProperty.call(e,o)&&(t[o]=e[o]);return t},a.apply(this,arguments)};
/*! *****************************************************************************
    Copyright (c) Microsoft Corporation.

    Permission to use, copy, modify, and/or distribute this software for any
    purpose with or without fee is hereby granted.

    THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
    REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
    AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
    INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
    LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
    OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
    PERFORMANCE OF THIS SOFTWARE.
    ***************************************************************************** */function s(t,e,n,i){return new(n||(n=Promise))((function(o,r){function a(t){try{c(i.next(t))}catch(t){r(t)}}function s(t){try{c(i.throw(t))}catch(t){r(t)}}function c(t){var e;t.done?o(t.value):(e=t.value,e instanceof n?e:new n((function(t){t(e)}))).then(a,s)}c((i=i.apply(t,e||[])).next())}))}function c(t,e){var n,i,o,r,a={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return r={next:s(0),throw:s(1),return:s(2)},"function"==typeof Symbol&&(r[Symbol.iterator]=function(){return this}),r;function s(r){return function(s){return function(r){if(n)throw new TypeError("Generator is already executing.");for(;a;)try{if(n=1,i&&(o=2&r[0]?i.return:r[0]?i.throw||((o=i.return)&&o.call(i),0):i.next)&&!(o=o.call(i,r[1])).done)return o;switch(i=0,o&&(r=[2&r[0],o.value]),r[0]){case 0:case 1:o=r;break;case 4:return a.label++,{value:r[1],done:!1};case 5:a.label++,i=r[1],r=[0];continue;case 7:r=a.ops.pop(),a.trys.pop();continue;default:if(!(o=a.trys,(o=o.length>0&&o[o.length-1])||6!==r[0]&&2!==r[0])){a=0;continue}if(3===r[0]&&(!o||r[1]>o[0]&&r[1]<o[3])){a.label=r[1];break}if(6===r[0]&&a.label<o[1]){a.label=o[1],o=r;break}if(o&&a.label<o[2]){a.label=o[2],a.ops.push(r);break}o[2]&&a.ops.pop(),a.trys.pop();continue}r=e.call(t,a)}catch(t){r=[6,t],i=0}finally{n=o=0}if(5&r[0])throw r[1];return{value:r[0]?r[1]:void 0,done:!0}}([r,s])}}}var l=new Proxy({value:!1},{get:function(){var t,e;return!(!window.HkAndroid&&!(null===(e=null===(t=window.webkit)||void 0===t?void 0:t.messageHandlers)||void 0===e?void 0:e.getBossInfoWithCallback))},set:function(){throw new Error('��䭾�訫��滚� "inApp.value"嚗��牐蛹摰��糓�蘨霂餃�墧�扼��')}}),d=function(){var t=navigator.userAgent;return t.indexOf("Android")>-1||t.indexOf("Adr")>-1||t.indexOf("hshhk/android")>-1?"android":t.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/)||t.indexOf("hshhk/ios/")>-1?"ios":"unknown"},u=function(){var t=navigator.userAgent.split("/");return t[t.length-1]},h=function(t,e,n){void 0===e&&(e="openhkhshlogin");var i=t||window.location.href,o=i;null!==e&&(o="".concat(e,"://").concat(i));var r="https://mylink.komect.com",a=document.getElementById("mylinkSdk");"daily"===(null==a?void 0:a.getAttribute("env"))&&(r="http://47.57.156.216");var s="".concat(r,"/mylink/#/l/?link=").concat(encodeURIComponent(o),"&umkey=").concat(n||"");window.location.href=s};function f(){location.href="cmcchkh5action://close"}function p(t,e,n){void 0===n&&(n={});var i=Object.entries(n).map((function(t){var e=t[0],n=t[1];return"".concat(e,"=").concat(n)})).join("&"),o=encodeURIComponent(encodeURIComponent(i));o&&(o="?".concat(o));var r=i;r&&(r="?".concat(r));var a=encodeURIComponent(encodeURIComponent("cmcchkhsh://confirmBeforeJump?version=7.0.1&utm_source=".concat(e.source,"&utm_medium=").concat(e.medium,"&utm_campaign=").concat(e.campaign,"&link=openhkhshlogin://").concat(t).concat(r)));return"https://l.mylinkapp.hk/?link=https://mylinkapp/?launchmylink://openhkhshlogin://".concat(t).concat(o,"&apn=com.ChinaMobile&amv=269&afl=https://mylink.komect.com/mylink/%23/s/?link%3D").concat(a,"&isi=483513425&ibi=com.chinamobile.csapp&utm_campaign=").concat(e.campaign,"&utm_medium=").concat(e.medium,"&utm_source=").concat(e.source)}var v=function(){return new Promise((function(t){var e=d();window.getVersionCallBack=function(e){t(Number(e.replace(/\./g,"")))};var n=JSON.stringify({callbackName:"getVersionCallBack"});"android"===e?null===window||void 0===window||window.HkAndroid.getVersionInfoWithCallback(n):"ios"===e&&t(Number(u().replace(/\./g,"")))}))},g="https://cdn.mylinkapp.hk/via/atv/common/share.png",y=function(t){var e,n,i,o;if("string"==typeof t&&(t={umkey:t}),console.log("logEventStatistics:",t),!t||!t.umkey)throw new Error("蝻箏�穃��㺭 umkey");var r=d();t=JSON.stringify(t),"android"===r?null===(e=window.HkAndroid)||void 0===e||e.logEventStatistics(t):"ios"===r?null===(o=null===(i=null===(n=window.webkit)||void 0===n?void 0:n.messageHandlers)||void 0===i?void 0:i.logEventStatistics)||void 0===o||o.postMessage(t):console.warn("getSystem()�𠳿��膶ndroid,銋罸�𦢓os,霂瑟��䰻navigator.userAgent�糓�炏蝚血�āetSystem隞���餉��")},w=new(function(){function t(){}return t.prototype.pageViewCallNavite=function(t){var e,n,i,o,r=d();"android"===r?null===(e=window.HkAndroid)||void 0===e||e.pageViewStatistics(t):"ios"===r?null===(o=null===(i=null===(n=window.webkit)||void 0===n?void 0:n.messageHandlers)||void 0===i?void 0:i.pageViewStatistics)||void 0===o||o.postMessage(t):console.warn("getSystem()�𠳿��膶ndroid,銋罸�𦢓os,霂瑟��䰻navigator.userAgent�糓�炏蝚血�āetSystem隞���餉��")},t.prototype.enter=function(t){console.log("PageStatistics enter",t),this.pageViewCallNavite(JSON.stringify({pageId:t,start:1}))},t.prototype.leave=function(t){console.log("PageStatistics leave",t),this.pageViewCallNavite(JSON.stringify({pageId:t,start:0}))},t}()),m=function(){function t(t){this.protocol={eowl:"openhkhshlogin",cmhk:"openlogin"},this.links=t}return Object.defineProperty(t.prototype,"protocolReg",{get:function(){var t=this,e=Object.keys(this.protocol);return new RegExp("(".concat(e.map((function(e){return"".concat(t.protocol[e],"://")})).join("|"),")"))},enumerable:!1,configurable:!0}),t.prototype.to=function(t){this.links&&Reflect.has(this.links,t)?window.location.href=this.links[t]:window.location.href=t},t.prototype.open=function(t,e,n){this.links&&Reflect.has(this.links,t)&&(t=this.links[t]);var i=this.protocolReg,o=i.test(t);if(l.value){n&&y(n);var r=o?t.replace(i,""):t;window.location.href=r}else{o&&(e=-1!==t.indexOf("openhkhshlogin")?"openhkhshlogin":"openlogin",t=t.replace(i,"")),h(t,e||null,n)}},t.prototype.appStore=function(){var t=d();"android"===t?window.location.href="https://play.google.com/store/apps/details?id=com.ChinaMobile":"ios"===t?window.location.href="https://apps.apple.com/hk/app/id483513425":console.warn("getSystem()�𠳿��膶ndroid,銋罸�𦢓os,霂瑟��䰻navigator.userAgent�糓�炏蝚血�āetSystem隞���餉��")},t}();function _(t,e){for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t.style[n]=e[n]);return t}var k={width:"100vw",height:"100vh",position:"absolute",top:0,left:0,fontSize:"1rem",display:"flex",alignItems:"center",justifyContent:"center",flexDirection:"column",zIndex:99999},b={width:"100vw",height:"100vh",position:"absolute",top:0,left:0,backgroundColor:"rgba(0, 0, 0, .8)",transition:"all 0.4s",opacity:0,zIndex:1},S={width:"60%",padding:"1.2em",backgroundColor:"#fff",borderRadius:"0.2em",textAlign:"center",fontSize:"1em",zIndex:2,opacity:0,transition:"all 0.4s 0.1s"},B={width:"60%",padding:"0.5em",borderRadius:"2em",border:"none",outline:"none",backgroundColor:"#6a44cf",color:"#ffffff",marginTop:"1em",fontSize:"0.8em"},x=function(){function t(t){this.lang="tc",this.rootStyle={fontSize:"1rem"},this.verText={sc:"�券�閬��凒�鰵�����鰵���𧋦��\nMyLink��滩�賢�銝擧迨瘣餃𢆡",tc:"�券�閬��凒�鰵�����鰵���𧋦��\nMyLink��滩�賢����迨瘣餃��",en:"You need to update the latest version of MyLink\nto participate in this campaign"},this.verBtnText={sc:"蝡见朖���漣",tc:"蝡见朖�����",en:"Upgrade Now"},this.overlayText={sc:"��甇�!\n�其�滨泵���暑�𢆡��銝舘��聢",tc:"��甇�!\n�其�滨泵���暑��訫������聢",en:"Sorry!\nYou are not eligible to participate in the campaign"},this.lang=(null==t?void 0:t.lang)||this.lang,this.verText=(null==t?void 0:t.verText)||this.verText,this.verBtnText=(null==t?void 0:t.verBtnText)||this.verBtnText,this.rootStyle=(null==t?void 0:t.rootStyle)||this.rootStyle,this.overlayText=(null==t?void 0:t.overlayText)||this.overlayText}return t.prototype.version=function(t){var e=this,n=function(t){var e=t.match(/(>|<|=)/g);return t=t.replace(/(>|<|=)/g,""),{symbol:(null==e?void 0:e.join(""))||">",version:t}}(t),i=n.symbol,o=n.version,r=function(t,e){switch(t){case">":return 1===e;case"<":return-1===e;case"=":return 0===e;case">=":return 1===e||0===e;case"<=":return-1===e||0===e}}(i,function(t,e){for(var n=t.split("."),i=e.split("."),o=Math.max(n.length,i.length);n.length<o;)n.push("0");for(;i.length<o;)i.push("0");for(var r=0;r<o;r++){var a=parseInt(n[r]),s=parseInt(i[r]);if(a>s)return 1;if(a<s)return-1}return 0}(u(),o));return new Promise((function(t,n){if(r)return t(!0);var i=document.createElement("div");_(i,a(a({},k),e.rootStyle));var o=document.createElement("div");_(o,b);var s=document.createElement("div");_(s,S);var c=document.createElement("p");c.innerText=e.verText[e.lang];var l=document.createElement("button");_(l,B),l.innerText=e.verBtnText[e.lang],s.appendChild(c),s.appendChild(l),l.addEventListener("click",(function(){(new m).appStore(),n(new Error("to app store")),f()})),o.addEventListener("click",(function(){n(new Error("close web view")),f()})),i.appendChild(o),i.appendChild(s),document.body.appendChild(i),document.body.style.overflow="hidden",setTimeout((function(){o.style.opacity="1",s.style.opacity="1"}),0)}))},t.prototype.overlay=function(t,e,n){var i=document.createElement("div");_(i,a(a(a({backgroundColor:"rgba(0, 0, 0, .8)"},k),this.rootStyle),e));var o=document.createElement("div");_(o,a({fontSize:"1em",fontWeight:"bold",color:"#fff",textAlign:"center",padding:"0 1em"},n)),o.innerText=t||this.overlayText[this.lang],i.appendChild(o),i.addEventListener("click",(function(){f()})),document.body.appendChild(i),document.body.style.overflow="hidden"},t}(),C={width:"100vw",height:"100vh",position:"fixed",top:0,left:0,fontSize:"1rem",display:"flex",alignItems:"center",justifyContent:"center",flexDirection:"column",zIndex:99999},A={width:"100vw",height:"100vh",position:"absolute",top:0,left:0,backgroundColor:"rgba(0, 0, 0, .8)",transition:"all 0.4s",opacity:0,zIndex:1},H=function(){function t(){}return t.loading=function(e,n,i){if(!t.loadingRoot){t.loadingRoot=document.createElement("div"),_(t.loadingRoot,a(a({backgroundColor:"rgba(0, 0, 0, .8)"},C),i));var o=document.createElement("div");_(o,A);var r=document.createElement("img");_(r,a({width:"2rem"},n)),r.src=e||"https://cdn.mylinkapp.hk/via/atv/common/loading.gif",t.loadingRoot.appendChild(o),t.loadingRoot.appendChild(r),document.body.appendChild(t.loadingRoot),document.body.style.overflow="hidden"}},t.closeLoading=function(){t.loadingRoot&&(document.body.removeChild(t.loadingRoot),document.body.style.overflow="",t.loadingRoot=void 0)},t}(),z="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function E(t){return t&&t.__esModule&&Object.prototype.hasOwnProperty.call(t,"default")?t.default:t}function R(){throw new Error("Dynamic requires are not currently supported by @rollup/plugin-commonjs")}var M=function(t,e,n){return t(n={path:e,exports:{},require:function(t,e){return R(null==e&&n.path)}},n.exports),n.exports}((function(t){function e(n){return t.exports=e="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},t.exports.__esModule=!0,t.exports.default=t.exports,e(n)}t.exports=e,t.exports.__esModule=!0,t.exports.default=t.exports})),O=E(M);document.addEventListener("DOMContentLoaded",(function(){var t=document.getElementById("mylinkSdk");if(!(null==t?void 0:t.getAttribute("close-arms"))){window.__bl={config:{pid:(null==t?void 0:t.getAttribute("ali-arms-pid"))||"jlhn8qavag@df79dc7cc1c2d45",uid:(null==t?void 0:t.getAttribute("ali-log-uid"))||"mylink",appType:"web",imgUrl:"https://arms-retcode.aliyuncs.com/r.png?",sendResource:!0,behavior:!0,enableSPA:!0,useFmp:!0,enableLinkTrace:!0,environment:(null==t?void 0:t.getAttribute("env"))||i.local,sample:(null==t?void 0:t.getAttribute("sample"))||10,parseResponse:function(t){return t?"object"!==O(t)?{msg:t}:{msg:JSON.stringify(t),code:t.code}:{msg:""}}}};var e=document.createElement("script");e.setAttribute("crossorigin",""),e.src="https://retcode.alicdn.com/retcode/bl.js";var n=document.body;n.insertBefore(e,n.firstChild)}}));var P=function(){function t(){}return t.config=function(t){var e,n,i;(null===(e=window.__bl)||void 0===e?void 0:e.setConfig)?null===(i=null===(n=window.__bl)||void 0===n?void 0:n.setConfig)||void 0===i||i.call(n,t):setTimeout((function(){var e,n;null===(n=null===(e=window.__bl)||void 0===e?void 0:e.setConfig)||void 0===n||n.call(e,t)}),3e3)},t.setEnvironment=function(e){t.config({environment:e})},t.setPid=function(e){t.config({pid:e})},t.enableSPA=function(e){t.config({enableSPA:e})},t.setUid=function(e){t.config({uid:e})},t.setPhone=function(e){t.config({setUsername:function(){return e}})},t.customParseResponse=function(e){t.config({parseResponse:e})},t.setSample=function(e){t.config({sample:e})},t.disabled=function(e){void 0===e&&(e=!0),t.config({disabled:e})},t}();function D(t,e){function n(n){var i=function(t,e){for(var n;t;){if(null==e?void 0:e(t))return!0;if("__vconsole"===t.id)return!0;if(null===(n=t.hasAttribute)||void 0===n?void 0:n.call(t,"ignore-jump-mylink-app"))return!0;t=t.parentNode}return!1}(n.target,e);i||(n.preventDefault(),n.stopPropagation(),t())}return document.addEventListener("click",n,!0),function(){document.removeEventListener("click",n,!0)}}window.CryptoJS=function(){var t,e,n,i,r,a,s,c,l,d,u=u||function(t,e){var n;if("undefined"!=typeof window&&window.crypto&&(n=window.crypto),!n&&"undefined"!=typeof window&&window.msCrypto&&(n=window.msCrypto),!n&&void 0!==z&&z.crypto&&(n=z.crypto),!n)try{n=o.default}catch(t){}var i=function(){if(n){if("function"==typeof n.getRandomValues)try{return n.getRandomValues(new Uint32Array(1))[0]}catch(t){}if("function"==typeof n.randomBytes)try{return n.randomBytes(4).readInt32LE()}catch(t){}}throw new Error("Native crypto module could not be used to get secure random number.")},r=Object.create||function(){function t(){}return function(e){var n;return t.prototype=e,n=new t,t.prototype=null,n}}(),a={},s=a.lib={},c=s.Base={extend:function(t){var e=r(this);return t&&e.mixIn(t),e.hasOwnProperty("init")&&this.init!==e.init||(e.init=function(){e.$super.init.apply(this,arguments)}),e.init.prototype=e,e.$super=this,e},create:function(){var t=this.extend();return t.init.apply(t,arguments),t},init:function(){},mixIn:function(t){for(var e in t)t.hasOwnProperty(e)&&(this[e]=t[e]);t.hasOwnProperty("toString")&&(this.toString=t.toString)},clone:function(){return this.init.prototype.extend(this)}},l=s.WordArray=c.extend({init:function(t,n){t=this.words=t||[],this.sigBytes=n!=e?n:4*t.length},toString:function(t){return(t||u).stringify(this)},concat:function(t){var e=this.words,n=t.words,i=this.sigBytes,o=t.sigBytes;if(this.clamp(),i%4)for(var r=0;r<o;r++){var a=n[r>>>2]>>>24-r%4*8&255;e[i+r>>>2]|=a<<24-(i+r)%4*8}else for(r=0;r<o;r+=4)e[i+r>>>2]=n[r>>>2];return this.sigBytes+=o,this},clamp:function(){var e=this.words,n=this.sigBytes;e[n>>>2]&=4294967295<<32-n%4*8,e.length=t.ceil(n/4)},clone:function(){var t=c.clone.call(this);return t.words=this.words.slice(0),t},random:function(t){for(var e=[],n=0;n<t;n+=4)e.push(i());return new l.init(e,t)}}),d=a.enc={},u=d.Hex={stringify:function(t){for(var e=t.words,n=t.sigBytes,i=[],o=0;o<n;o++){var r=e[o>>>2]>>>24-o%4*8&255;i.push((r>>>4).toString(16)),i.push((15&r).toString(16))}return i.join("")},parse:function(t){for(var e=t.length,n=[],i=0;i<e;i+=2)n[i>>>3]|=parseInt(t.substr(i,2),16)<<24-i%8*4;return new l.init(n,e/2)}},h=d.Latin1={stringify:function(t){for(var e=t.words,n=t.sigBytes,i=[],o=0;o<n;o++){var r=e[o>>>2]>>>24-o%4*8&255;i.push(String.fromCharCode(r))}return i.join("")},parse:function(t){for(var e=t.length,n=[],i=0;i<e;i++)n[i>>>2]|=(255&t.charCodeAt(i))<<24-i%4*8;return new l.init(n,e)}},f=d.Utf8={stringify:function(t){try{return decodeURIComponent(escape(h.stringify(t)))}catch(t){throw new Error("Malformed UTF-8 data")}},parse:function(t){return h.parse(unescape(encodeURIComponent(t)))}},p=s.BufferedBlockAlgorithm=c.extend({reset:function(){this._data=new l.init,this._nDataBytes=0},_append:function(t){"string"==typeof t&&(t=f.parse(t)),this._data.concat(t),this._nDataBytes+=t.sigBytes},_process:function(e){var n,i=this._data,o=i.words,r=i.sigBytes,a=this.blockSize,s=r/(4*a),c=(s=e?t.ceil(s):t.max((0|s)-this._minBufferSize,0))*a,d=t.min(4*c,r);if(c){for(var u=0;u<c;u+=a)this._doProcessBlock(o,u);n=o.splice(0,c),i.sigBytes-=d}return new l.init(n,d)},clone:function(){var t=c.clone.call(this);return t._data=this._data.clone(),t},_minBufferSize:0});s.Hasher=p.extend({cfg:c.extend(),init:function(t){this.cfg=this.cfg.extend(t),this.reset()},reset:function(){p.reset.call(this),this._doReset()},update:function(t){return this._append(t),this._process(),this},finalize:function(t){return t&&this._append(t),this._doFinalize()},blockSize:16,_createHelper:function(t){return function(e,n){return new t.init(n).finalize(e)}},_createHmacHelper:function(t){return function(e,n){return new v.HMAC.init(t,n).finalize(e)}}});var v=a.algo={};return a}(Math);return function(){var t=u,e=t.lib.WordArray;function n(t,n,i){for(var o=[],r=0,a=0;a<n;a++)if(a%4){var s=i[t.charCodeAt(a-1)]<<a%4*2|i[t.charCodeAt(a)]>>>6-a%4*2;o[r>>>2]|=s<<24-r%4*8,r++}return e.create(o,r)}t.enc.Base64={stringify:function(t){var e=t.words,n=t.sigBytes,i=this._map;t.clamp();for(var o=[],r=0;r<n;r+=3)for(var a=(e[r>>>2]>>>24-r%4*8&255)<<16|(e[r+1>>>2]>>>24-(r+1)%4*8&255)<<8|e[r+2>>>2]>>>24-(r+2)%4*8&255,s=0;s<4&&r+.75*s<n;s++)o.push(i.charAt(a>>>6*(3-s)&63));var c=i.charAt(64);if(c)for(;o.length%4;)o.push(c);return o.join("")},parse:function(t){var e=t.length,i=this._map,o=this._reverseMap;if(!o){o=this._reverseMap=[];for(var r=0;r<i.length;r++)o[i.charCodeAt(r)]=r}var a=i.charAt(64);if(a){var s=t.indexOf(a);-1!==s&&(e=s)}return n(t,e,o)},_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/="}}(),function(t){var e=u,n=e.lib,i=n.WordArray,o=n.Hasher,r=e.algo,a=[];!function(){for(var e=0;e<64;e++)a[e]=4294967296*t.abs(t.sin(e+1))|0}();var s=r.MD5=o.extend({_doReset:function(){this._hash=new i.init([1732584193,4023233417,2562383102,271733878])},_doProcessBlock:function(t,e){for(var n=0;n<16;n++){var i=e+n,o=t[i];t[i]=16711935&(o<<8|o>>>24)|4278255360&(o<<24|o>>>8)}var r=this._hash.words,s=t[e+0],u=t[e+1],f=t[e+2],p=t[e+3],v=t[e+4],g=t[e+5],y=t[e+6],w=t[e+7],m=t[e+8],_=t[e+9],k=t[e+10],b=t[e+11],S=t[e+12],B=t[e+13],x=t[e+14],C=t[e+15],A=r[0],H=r[1],z=r[2],E=r[3];A=c(A,H,z,E,s,7,a[0]),E=c(E,A,H,z,u,12,a[1]),z=c(z,E,A,H,f,17,a[2]),H=c(H,z,E,A,p,22,a[3]),A=c(A,H,z,E,v,7,a[4]),E=c(E,A,H,z,g,12,a[5]),z=c(z,E,A,H,y,17,a[6]),H=c(H,z,E,A,w,22,a[7]),A=c(A,H,z,E,m,7,a[8]),E=c(E,A,H,z,_,12,a[9]),z=c(z,E,A,H,k,17,a[10]),H=c(H,z,E,A,b,22,a[11]),A=c(A,H,z,E,S,7,a[12]),E=c(E,A,H,z,B,12,a[13]),z=c(z,E,A,H,x,17,a[14]),A=l(A,H=c(H,z,E,A,C,22,a[15]),z,E,u,5,a[16]),E=l(E,A,H,z,y,9,a[17]),z=l(z,E,A,H,b,14,a[18]),H=l(H,z,E,A,s,20,a[19]),A=l(A,H,z,E,g,5,a[20]),E=l(E,A,H,z,k,9,a[21]),z=l(z,E,A,H,C,14,a[22]),H=l(H,z,E,A,v,20,a[23]),A=l(A,H,z,E,_,5,a[24]),E=l(E,A,H,z,x,9,a[25]),z=l(z,E,A,H,p,14,a[26]),H=l(H,z,E,A,m,20,a[27]),A=l(A,H,z,E,B,5,a[28]),E=l(E,A,H,z,f,9,a[29]),z=l(z,E,A,H,w,14,a[30]),A=d(A,H=l(H,z,E,A,S,20,a[31]),z,E,g,4,a[32]),E=d(E,A,H,z,m,11,a[33]),z=d(z,E,A,H,b,16,a[34]),H=d(H,z,E,A,x,23,a[35]),A=d(A,H,z,E,u,4,a[36]),E=d(E,A,H,z,v,11,a[37]),z=d(z,E,A,H,w,16,a[38]),H=d(H,z,E,A,k,23,a[39]),A=d(A,H,z,E,B,4,a[40]),E=d(E,A,H,z,s,11,a[41]),z=d(z,E,A,H,p,16,a[42]),H=d(H,z,E,A,y,23,a[43]),A=d(A,H,z,E,_,4,a[44]),E=d(E,A,H,z,S,11,a[45]),z=d(z,E,A,H,C,16,a[46]),A=h(A,H=d(H,z,E,A,f,23,a[47]),z,E,s,6,a[48]),E=h(E,A,H,z,w,10,a[49]),z=h(z,E,A,H,x,15,a[50]),H=h(H,z,E,A,g,21,a[51]),A=h(A,H,z,E,S,6,a[52]),E=h(E,A,H,z,p,10,a[53]),z=h(z,E,A,H,k,15,a[54]),H=h(H,z,E,A,u,21,a[55]),A=h(A,H,z,E,m,6,a[56]),E=h(E,A,H,z,C,10,a[57]),z=h(z,E,A,H,y,15,a[58]),H=h(H,z,E,A,B,21,a[59]),A=h(A,H,z,E,v,6,a[60]),E=h(E,A,H,z,b,10,a[61]),z=h(z,E,A,H,f,15,a[62]),H=h(H,z,E,A,_,21,a[63]),r[0]=r[0]+A|0,r[1]=r[1]+H|0,r[2]=r[2]+z|0,r[3]=r[3]+E|0},_doFinalize:function(){var e=this._data,n=e.words,i=8*this._nDataBytes,o=8*e.sigBytes;n[o>>>5]|=128<<24-o%32;var r=t.floor(i/4294967296),a=i;n[15+(o+64>>>9<<4)]=16711935&(r<<8|r>>>24)|4278255360&(r<<24|r>>>8),n[14+(o+64>>>9<<4)]=16711935&(a<<8|a>>>24)|4278255360&(a<<24|a>>>8),e.sigBytes=4*(n.length+1),this._process();for(var s=this._hash,c=s.words,l=0;l<4;l++){var d=c[l];c[l]=16711935&(d<<8|d>>>24)|4278255360&(d<<24|d>>>8)}return s},clone:function(){var t=o.clone.call(this);return t._hash=this._hash.clone(),t}});function c(t,e,n,i,o,r,a){var s=t+(e&n|~e&i)+o+a;return(s<<r|s>>>32-r)+e}function l(t,e,n,i,o,r,a){var s=t+(e&i|n&~i)+o+a;return(s<<r|s>>>32-r)+e}function d(t,e,n,i,o,r,a){var s=t+(e^n^i)+o+a;return(s<<r|s>>>32-r)+e}function h(t,e,n,i,o,r,a){var s=t+(n^(e|~i))+o+a;return(s<<r|s>>>32-r)+e}e.MD5=o._createHelper(s),e.HmacMD5=o._createHmacHelper(s)}(Math),r=(i=u).lib,a=r.WordArray,s=r.Hasher,c=i.algo,l=[],d=c.SHA1=s.extend({_doReset:function(){this._hash=new a.init([1732584193,4023233417,2562383102,271733878,3285377520])},_doProcessBlock:function(t,e){for(var n=this._hash.words,i=n[0],o=n[1],r=n[2],a=n[3],s=n[4],c=0;c<80;c++){if(c<16)l[c]=0|t[e+c];else{var d=l[c-3]^l[c-8]^l[c-14]^l[c-16];l[c]=d<<1|d>>>31}var u=(i<<5|i>>>27)+s+l[c];u+=c<20?1518500249+(o&r|~o&a):c<40?1859775393+(o^r^a):c<60?(o&r|o&a|r&a)-1894007588:(o^r^a)-899497514,s=a,a=r,r=o<<30|o>>>2,o=i,i=u}n[0]=n[0]+i|0,n[1]=n[1]+o|0,n[2]=n[2]+r|0,n[3]=n[3]+a|0,n[4]=n[4]+s|0},_doFinalize:function(){var t=this._data,e=t.words,n=8*this._nDataBytes,i=8*t.sigBytes;return e[i>>>5]|=128<<24-i%32,e[14+(i+64>>>9<<4)]=Math.floor(n/4294967296),e[15+(i+64>>>9<<4)]=n,t.sigBytes=4*e.length,this._process(),this._hash},clone:function(){var t=s.clone.call(this);return t._hash=this._hash.clone(),t}}),i.SHA1=s._createHelper(d),i.HmacSHA1=s._createHmacHelper(d),function(t){var e=u,n=e.lib,i=n.WordArray,o=n.Hasher,r=e.algo,a=[],s=[];!function(){function e(e){for(var n=t.sqrt(e),i=2;i<=n;i++)if(!(e%i))return!1;return!0}function n(t){return 4294967296*(t-(0|t))|0}for(var i=2,o=0;o<64;)e(i)&&(o<8&&(a[o]=n(t.pow(i,.5))),s[o]=n(t.pow(i,1/3)),o++),i++}();var c=[],l=r.SHA256=o.extend({_doReset:function(){this._hash=new i.init(a.slice(0))},_doProcessBlock:function(t,e){for(var n=this._hash.words,i=n[0],o=n[1],r=n[2],a=n[3],l=n[4],d=n[5],u=n[6],h=n[7],f=0;f<64;f++){if(f<16)c[f]=0|t[e+f];else{var p=c[f-15],v=(p<<25|p>>>7)^(p<<14|p>>>18)^p>>>3,g=c[f-2],y=(g<<15|g>>>17)^(g<<13|g>>>19)^g>>>10;c[f]=v+c[f-7]+y+c[f-16]}var w=i&o^i&r^o&r,m=(i<<30|i>>>2)^(i<<19|i>>>13)^(i<<10|i>>>22),_=h+((l<<26|l>>>6)^(l<<21|l>>>11)^(l<<7|l>>>25))+(l&d^~l&u)+s[f]+c[f];h=u,u=d,d=l,l=a+_|0,a=r,r=o,o=i,i=_+(m+w)|0}n[0]=n[0]+i|0,n[1]=n[1]+o|0,n[2]=n[2]+r|0,n[3]=n[3]+a|0,n[4]=n[4]+l|0,n[5]=n[5]+d|0,n[6]=n[6]+u|0,n[7]=n[7]+h|0},_doFinalize:function(){var e=this._data,n=e.words,i=8*this._nDataBytes,o=8*e.sigBytes;return n[o>>>5]|=128<<24-o%32,n[14+(o+64>>>9<<4)]=t.floor(i/4294967296),n[15+(o+64>>>9<<4)]=i,e.sigBytes=4*n.length,this._process(),this._hash},clone:function(){var t=o.clone.call(this);return t._hash=this._hash.clone(),t}});e.SHA256=o._createHelper(l),e.HmacSHA256=o._createHmacHelper(l)}(Math),function(){var t=u,e=t.lib.WordArray,n=t.enc;function i(t){return t<<8&4278255360|t>>>8&16711935}n.Utf16=n.Utf16BE={stringify:function(t){for(var e=t.words,n=t.sigBytes,i=[],o=0;o<n;o+=2){var r=e[o>>>2]>>>16-o%4*8&65535;i.push(String.fromCharCode(r))}return i.join("")},parse:function(t){for(var n=t.length,i=[],o=0;o<n;o++)i[o>>>1]|=t.charCodeAt(o)<<16-o%2*16;return e.create(i,2*n)}},n.Utf16LE={stringify:function(t){for(var e=t.words,n=t.sigBytes,o=[],r=0;r<n;r+=2){var a=i(e[r>>>2]>>>16-r%4*8&65535);o.push(String.fromCharCode(a))}return o.join("")},parse:function(t){for(var n=t.length,o=[],r=0;r<n;r++)o[r>>>1]|=i(t.charCodeAt(r)<<16-r%2*16);return e.create(o,2*n)}}}(),function(){if("function"==typeof ArrayBuffer){var t=u.lib.WordArray,e=t.init,n=t.init=function(t){if(t instanceof ArrayBuffer&&(t=new Uint8Array(t)),(t instanceof Int8Array||"undefined"!=typeof Uint8ClampedArray&&t instanceof Uint8ClampedArray||t instanceof Int16Array||t instanceof Uint16Array||t instanceof Int32Array||t instanceof Uint32Array||t instanceof Float32Array||t instanceof Float64Array)&&(t=new Uint8Array(t.buffer,t.byteOffset,t.byteLength)),t instanceof Uint8Array){for(var n=t.byteLength,i=[],o=0;o<n;o++)i[o>>>2]|=t[o]<<24-o%4*8;e.call(this,i,n)}else e.apply(this,arguments)};n.prototype=t}}(),
/** @preserve
      (c) 2012 by C矇dric Mesnil. All rights reserved.
      	Redistribution and use in source and binary forms, with or without modification, are permitted provided that the following conditions are met:
      	    - Redistributions of source code must retain the above copyright notice, this list of conditions and the following disclaimer.
          - Redistributions in binary form must reproduce the above copyright notice, this list of conditions and the following disclaimer in the documentation and/or other materials provided with the distribution.
      	THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
      */
function(t){var e=u,n=e.lib,i=n.WordArray,o=n.Hasher,r=e.algo,a=i.create([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,7,4,13,1,10,6,15,3,12,0,9,5,2,14,11,8,3,10,14,4,9,15,8,1,2,7,0,6,13,11,5,12,1,9,11,10,0,8,12,4,13,3,7,15,14,5,6,2,4,0,5,9,7,12,2,10,14,1,3,8,11,6,15,13]),s=i.create([5,14,7,0,9,2,11,4,13,6,15,8,1,10,3,12,6,11,3,7,0,13,5,10,14,15,8,12,4,9,1,2,15,5,1,3,7,14,6,9,11,8,12,2,10,0,4,13,8,6,4,1,3,11,15,0,5,12,2,13,9,7,10,14,12,15,10,4,1,5,8,7,6,2,13,14,0,3,9,11]),c=i.create([11,14,15,12,5,8,7,9,11,13,14,15,6,7,9,8,7,6,8,13,11,9,7,15,7,12,15,9,11,7,13,12,11,13,6,7,14,9,13,15,14,8,13,6,5,12,7,5,11,12,14,15,14,15,9,8,9,14,5,6,8,6,5,12,9,15,5,11,6,8,13,12,5,12,13,14,11,8,5,6]),l=i.create([8,9,9,11,13,15,15,5,7,7,8,11,14,14,12,6,9,13,15,7,12,8,9,11,7,7,12,7,6,15,13,11,9,7,15,11,8,6,6,14,12,13,5,14,13,13,7,5,15,5,8,11,14,14,6,14,6,9,12,9,12,5,15,8,8,5,12,9,12,5,14,6,8,13,6,5,15,13,11,11]),d=i.create([0,1518500249,1859775393,2400959708,2840853838]),h=i.create([1352829926,1548603684,1836072691,2053994217,0]),f=r.RIPEMD160=o.extend({_doReset:function(){this._hash=i.create([1732584193,4023233417,2562383102,271733878,3285377520])},_doProcessBlock:function(t,e){for(var n=0;n<16;n++){var i=e+n,o=t[i];t[i]=16711935&(o<<8|o>>>24)|4278255360&(o<<24|o>>>8)}var r,u,f,_,k,b,S,B,x,C,A,H=this._hash.words,z=d.words,E=h.words,R=a.words,M=s.words,O=c.words,P=l.words;for(b=r=H[0],S=u=H[1],B=f=H[2],x=_=H[3],C=k=H[4],n=0;n<80;n+=1)A=r+t[e+R[n]]|0,A+=n<16?p(u,f,_)+z[0]:n<32?v(u,f,_)+z[1]:n<48?g(u,f,_)+z[2]:n<64?y(u,f,_)+z[3]:w(u,f,_)+z[4],A=(A=m(A|=0,O[n]))+k|0,r=k,k=_,_=m(f,10),f=u,u=A,A=b+t[e+M[n]]|0,A+=n<16?w(S,B,x)+E[0]:n<32?y(S,B,x)+E[1]:n<48?g(S,B,x)+E[2]:n<64?v(S,B,x)+E[3]:p(S,B,x)+E[4],A=(A=m(A|=0,P[n]))+C|0,b=C,C=x,x=m(B,10),B=S,S=A;A=H[1]+f+x|0,H[1]=H[2]+_+C|0,H[2]=H[3]+k+b|0,H[3]=H[4]+r+S|0,H[4]=H[0]+u+B|0,H[0]=A},_doFinalize:function(){var t=this._data,e=t.words,n=8*this._nDataBytes,i=8*t.sigBytes;e[i>>>5]|=128<<24-i%32,e[14+(i+64>>>9<<4)]=16711935&(n<<8|n>>>24)|4278255360&(n<<24|n>>>8),t.sigBytes=4*(e.length+1),this._process();for(var o=this._hash,r=o.words,a=0;a<5;a++){var s=r[a];r[a]=16711935&(s<<8|s>>>24)|4278255360&(s<<24|s>>>8)}return o},clone:function(){var t=o.clone.call(this);return t._hash=this._hash.clone(),t}});function p(t,e,n){return t^e^n}function v(t,e,n){return t&e|~t&n}function g(t,e,n){return(t|~e)^n}function y(t,e,n){return t&n|e&~n}function w(t,e,n){return t^(e|~n)}function m(t,e){return t<<e|t>>>32-e}e.RIPEMD160=o._createHelper(f),e.HmacRIPEMD160=o._createHmacHelper(f)}(),function(){var t=u,e=t.lib.Base,n=t.enc.Utf8;t.algo.HMAC=e.extend({init:function(t,e){t=this._hasher=new t.init,"string"==typeof e&&(e=n.parse(e));var i=t.blockSize,o=4*i;e.sigBytes>o&&(e=t.finalize(e)),e.clamp();for(var r=this._oKey=e.clone(),a=this._iKey=e.clone(),s=r.words,c=a.words,l=0;l<i;l++)s[l]^=1549556828,c[l]^=909522486;r.sigBytes=a.sigBytes=o,this.reset()},reset:function(){var t=this._hasher;t.reset(),t.update(this._iKey)},update:function(t){return this._hasher.update(t),this},finalize:function(t){var e=this._hasher,n=e.finalize(t);return e.reset(),e.finalize(this._oKey.clone().concat(n))}})}(),function(){var t=u,e=t.lib,n=e.Base,i=e.WordArray,o=t.algo,r=o.SHA1,a=o.HMAC,s=o.PBKDF2=n.extend({cfg:n.extend({keySize:4,hasher:r,iterations:1}),init:function(t){this.cfg=this.cfg.extend(t)},compute:function(t,e){for(var n=this.cfg,o=a.create(n.hasher,t),r=i.create(),s=i.create([1]),c=r.words,l=s.words,d=n.keySize,u=n.iterations;c.length<d;){var h=o.update(e).finalize(s);o.reset();for(var f=h.words,p=f.length,v=h,g=1;g<u;g++){v=o.finalize(v),o.reset();for(var y=v.words,w=0;w<p;w++)f[w]^=y[w]}r.concat(h),l[0]++}return r.sigBytes=4*d,r}});t.PBKDF2=function(t,e,n){return s.create(n).compute(t,e)}}(),function(){var t=u,e=t.lib,n=e.Base,i=e.WordArray,o=t.algo,r=o.MD5,a=o.EvpKDF=n.extend({cfg:n.extend({keySize:4,hasher:r,iterations:1}),init:function(t){this.cfg=this.cfg.extend(t)},compute:function(t,e){for(var n,o=this.cfg,r=o.hasher.create(),a=i.create(),s=a.words,c=o.keySize,l=o.iterations;s.length<c;){n&&r.update(n),n=r.update(t).finalize(e),r.reset();for(var d=1;d<l;d++)n=r.finalize(n),r.reset();a.concat(n)}return a.sigBytes=4*c,a}});t.EvpKDF=function(t,e,n){return a.create(n).compute(t,e)}}(),function(){var t=u,e=t.lib.WordArray,n=t.algo,i=n.SHA256,o=n.SHA224=i.extend({_doReset:function(){this._hash=new e.init([3238371032,914150663,812702999,4144912697,4290775857,1750603025,1694076839,3204075428])},_doFinalize:function(){var t=i._doFinalize.call(this);return t.sigBytes-=4,t}});t.SHA224=i._createHelper(o),t.HmacSHA224=i._createHmacHelper(o)}(),function(t){var e=u,n=e.lib,i=n.Base,o=n.WordArray,r=e.x64={};r.Word=i.extend({init:function(t,e){this.high=t,this.low=e}}),r.WordArray=i.extend({init:function(e,n){e=this.words=e||[],this.sigBytes=n!=t?n:8*e.length},toX32:function(){for(var t=this.words,e=t.length,n=[],i=0;i<e;i++){var r=t[i];n.push(r.high),n.push(r.low)}return o.create(n,this.sigBytes)},clone:function(){for(var t=i.clone.call(this),e=t.words=this.words.slice(0),n=e.length,o=0;o<n;o++)e[o]=e[o].clone();return t}})}(),function(t){var e=u,n=e.lib,i=n.WordArray,o=n.Hasher,r=e.x64.Word,a=e.algo,s=[],c=[],l=[];!function(){for(var t=1,e=0,n=0;n<24;n++){s[t+5*e]=(n+1)*(n+2)/2%64;var i=(2*t+3*e)%5;t=e%5,e=i}for(t=0;t<5;t++)for(e=0;e<5;e++)c[t+5*e]=e+(2*t+3*e)%5*5;for(var o=1,a=0;a<24;a++){for(var d=0,u=0,h=0;h<7;h++){if(1&o){var f=(1<<h)-1;f<32?u^=1<<f:d^=1<<f-32}128&o?o=o<<1^113:o<<=1}l[a]=r.create(d,u)}}();var d=[];!function(){for(var t=0;t<25;t++)d[t]=r.create()}();var h=a.SHA3=o.extend({cfg:o.cfg.extend({outputLength:512}),_doReset:function(){for(var t=this._state=[],e=0;e<25;e++)t[e]=new r.init;this.blockSize=(1600-2*this.cfg.outputLength)/32},_doProcessBlock:function(t,e){for(var n=this._state,i=this.blockSize/2,o=0;o<i;o++){var r=t[e+2*o],a=t[e+2*o+1];r=16711935&(r<<8|r>>>24)|4278255360&(r<<24|r>>>8),a=16711935&(a<<8|a>>>24)|4278255360&(a<<24|a>>>8),(H=n[o]).high^=a,H.low^=r}for(var u=0;u<24;u++){for(var h=0;h<5;h++){for(var f=0,p=0,v=0;v<5;v++)f^=(H=n[h+5*v]).high,p^=H.low;var g=d[h];g.high=f,g.low=p}for(h=0;h<5;h++){var y=d[(h+4)%5],w=d[(h+1)%5],m=w.high,_=w.low;for(f=y.high^(m<<1|_>>>31),p=y.low^(_<<1|m>>>31),v=0;v<5;v++)(H=n[h+5*v]).high^=f,H.low^=p}for(var k=1;k<25;k++){var b=(H=n[k]).high,S=H.low,B=s[k];B<32?(f=b<<B|S>>>32-B,p=S<<B|b>>>32-B):(f=S<<B-32|b>>>64-B,p=b<<B-32|S>>>64-B);var x=d[c[k]];x.high=f,x.low=p}var C=d[0],A=n[0];for(C.high=A.high,C.low=A.low,h=0;h<5;h++)for(v=0;v<5;v++){var H=n[k=h+5*v],z=d[k],E=d[(h+1)%5+5*v],R=d[(h+2)%5+5*v];H.high=z.high^~E.high&R.high,H.low=z.low^~E.low&R.low}H=n[0];var M=l[u];H.high^=M.high,H.low^=M.low}},_doFinalize:function(){var e=this._data,n=e.words;this._nDataBytes;var o=8*e.sigBytes,r=32*this.blockSize;n[o>>>5]|=1<<24-o%32,n[(t.ceil((o+1)/r)*r>>>5)-1]|=128,e.sigBytes=4*n.length,this._process();for(var a=this._state,s=this.cfg.outputLength/8,c=s/8,l=[],d=0;d<c;d++){var u=a[d],h=u.high,f=u.low;h=16711935&(h<<8|h>>>24)|4278255360&(h<<24|h>>>8),f=16711935&(f<<8|f>>>24)|4278255360&(f<<24|f>>>8),l.push(f),l.push(h)}return new i.init(l,s)},clone:function(){for(var t=o.clone.call(this),e=t._state=this._state.slice(0),n=0;n<25;n++)e[n]=e[n].clone();return t}});e.SHA3=o._createHelper(h),e.HmacSHA3=o._createHmacHelper(h)}(Math),function(){var t=u,e=t.lib.Hasher,n=t.x64,i=n.Word,o=n.WordArray,r=t.algo;function a(){return i.create.apply(i,arguments)}var s=[a(1116352408,3609767458),a(1899447441,602891725),a(3049323471,3964484399),a(3921009573,2173295548),a(961987163,4081628472),a(1508970993,3053834265),a(2453635748,2937671579),a(2870763221,3664609560),a(3624381080,2734883394),a(310598401,1164996542),a(607225278,1323610764),a(1426881987,3590304994),a(1925078388,4068182383),a(2162078206,991336113),a(2614888103,633803317),a(3248222580,3479774868),a(3835390401,2666613458),a(4022224774,944711139),a(264347078,2341262773),a(604807628,2007800933),a(770255983,1495990901),a(1249150122,1856431235),a(1555081692,3175218132),a(1996064986,2198950837),a(2554220882,3999719339),a(2821834349,766784016),a(2952996808,2566594879),a(3210313671,3203337956),a(3336571891,1034457026),a(3584528711,2466948901),a(113926993,3758326383),a(338241895,168717936),a(666307205,1188179964),a(773529912,1546045734),a(1294757372,1522805485),a(1396182291,2643833823),a(1695183700,2343527390),a(1986661051,1014477480),a(2177026350,1206759142),a(2456956037,344077627),a(2730485921,1290863460),a(2820302411,3158454273),a(3259730800,3505952657),a(3345764771,106217008),a(3516065817,3606008344),a(3600352804,1432725776),a(4094571909,1467031594),a(275423344,851169720),a(430227734,3100823752),a(506948616,1363258195),a(659060556,3750685593),a(883997877,3785050280),a(958139571,3318307427),a(1322822218,3812723403),a(1537002063,2003034995),a(1747873779,3602036899),a(1955562222,1575990012),a(2024104815,1125592928),a(2227730452,2716904306),a(2361852424,442776044),a(2428436474,593698344),a(2756734187,3733110249),a(3204031479,2999351573),a(3329325298,3815920427),a(3391569614,3928383900),a(3515267271,566280711),a(3940187606,3454069534),a(4118630271,4000239992),a(116418474,1914138554),a(174292421,2731055270),a(289380356,3203993006),a(460393269,320620315),a(685471733,587496836),a(852142971,1086792851),a(1017036298,365543100),a(1126000580,2618297676),a(1288033470,3409855158),a(1501505948,4234509866),a(1607167915,987167468),a(1816402316,1246189591)],c=[];!function(){for(var t=0;t<80;t++)c[t]=a()}();var l=r.SHA512=e.extend({_doReset:function(){this._hash=new o.init([new i.init(1779033703,4089235720),new i.init(3144134277,2227873595),new i.init(1013904242,4271175723),new i.init(2773480762,1595750129),new i.init(1359893119,2917565137),new i.init(2600822924,725511199),new i.init(528734635,4215389547),new i.init(1541459225,327033209)])},_doProcessBlock:function(t,e){for(var n=this._hash.words,i=n[0],o=n[1],r=n[2],a=n[3],l=n[4],d=n[5],u=n[6],h=n[7],f=i.high,p=i.low,v=o.high,g=o.low,y=r.high,w=r.low,m=a.high,_=a.low,k=l.high,b=l.low,S=d.high,B=d.low,x=u.high,C=u.low,A=h.high,H=h.low,z=f,E=p,R=v,M=g,O=y,P=w,D=m,I=_,N=k,L=b,T=S,U=B,J=x,j=C,W=A,F=H,X=0;X<80;X++){var K,V,q=c[X];if(X<16)V=q.high=0|t[e+2*X],K=q.low=0|t[e+2*X+1];else{var Z=c[X-15],$=Z.high,G=Z.low,Y=($>>>1|G<<31)^($>>>8|G<<24)^$>>>7,Q=(G>>>1|$<<31)^(G>>>8|$<<24)^(G>>>7|$<<25),tt=c[X-2],et=tt.high,nt=tt.low,it=(et>>>19|nt<<13)^(et<<3|nt>>>29)^et>>>6,ot=(nt>>>19|et<<13)^(nt<<3|et>>>29)^(nt>>>6|et<<26),rt=c[X-7],at=rt.high,st=rt.low,ct=c[X-16],lt=ct.high,dt=ct.low;V=(V=(V=Y+at+((K=Q+st)>>>0<Q>>>0?1:0))+it+((K+=ot)>>>0<ot>>>0?1:0))+lt+((K+=dt)>>>0<dt>>>0?1:0),q.high=V,q.low=K}var ut,ht=N&T^~N&J,ft=L&U^~L&j,pt=z&R^z&O^R&O,vt=E&M^E&P^M&P,gt=(z>>>28|E<<4)^(z<<30|E>>>2)^(z<<25|E>>>7),yt=(E>>>28|z<<4)^(E<<30|z>>>2)^(E<<25|z>>>7),wt=(N>>>14|L<<18)^(N>>>18|L<<14)^(N<<23|L>>>9),mt=(L>>>14|N<<18)^(L>>>18|N<<14)^(L<<23|N>>>9),_t=s[X],kt=_t.high,bt=_t.low,St=W+wt+((ut=F+mt)>>>0<F>>>0?1:0),Bt=yt+vt;W=J,F=j,J=T,j=U,T=N,U=L,N=D+(St=(St=(St=St+ht+((ut+=ft)>>>0<ft>>>0?1:0))+kt+((ut+=bt)>>>0<bt>>>0?1:0))+V+((ut+=K)>>>0<K>>>0?1:0))+((L=I+ut|0)>>>0<I>>>0?1:0)|0,D=O,I=P,O=R,P=M,R=z,M=E,z=St+(gt+pt+(Bt>>>0<yt>>>0?1:0))+((E=ut+Bt|0)>>>0<ut>>>0?1:0)|0}p=i.low=p+E,i.high=f+z+(p>>>0<E>>>0?1:0),g=o.low=g+M,o.high=v+R+(g>>>0<M>>>0?1:0),w=r.low=w+P,r.high=y+O+(w>>>0<P>>>0?1:0),_=a.low=_+I,a.high=m+D+(_>>>0<I>>>0?1:0),b=l.low=b+L,l.high=k+N+(b>>>0<L>>>0?1:0),B=d.low=B+U,d.high=S+T+(B>>>0<U>>>0?1:0),C=u.low=C+j,u.high=x+J+(C>>>0<j>>>0?1:0),H=h.low=H+F,h.high=A+W+(H>>>0<F>>>0?1:0)},_doFinalize:function(){var t=this._data,e=t.words,n=8*this._nDataBytes,i=8*t.sigBytes;return e[i>>>5]|=128<<24-i%32,e[30+(i+128>>>10<<5)]=Math.floor(n/4294967296),e[31+(i+128>>>10<<5)]=n,t.sigBytes=4*e.length,this._process(),this._hash.toX32()},clone:function(){var t=e.clone.call(this);return t._hash=this._hash.clone(),t},blockSize:32});t.SHA512=e._createHelper(l),t.HmacSHA512=e._createHmacHelper(l)}(),function(){var t=u,e=t.x64,n=e.Word,i=e.WordArray,o=t.algo,r=o.SHA512,a=o.SHA384=r.extend({_doReset:function(){this._hash=new i.init([new n.init(3418070365,3238371032),new n.init(1654270250,914150663),new n.init(2438529370,812702999),new n.init(355462360,4144912697),new n.init(1731405415,4290775857),new n.init(2394180231,1750603025),new n.init(3675008525,1694076839),new n.init(1203062813,3204075428)])},_doFinalize:function(){var t=r._doFinalize.call(this);return t.sigBytes-=16,t}});t.SHA384=r._createHelper(a),t.HmacSHA384=r._createHmacHelper(a)}(),u.lib.Cipher||function(t){var e=u,n=e.lib,i=n.Base,o=n.WordArray,r=n.BufferedBlockAlgorithm,a=e.enc;a.Utf8;var s=a.Base64,c=e.algo.EvpKDF,l=n.Cipher=r.extend({cfg:i.extend(),createEncryptor:function(t,e){return this.create(this._ENC_XFORM_MODE,t,e)},createDecryptor:function(t,e){return this.create(this._DEC_XFORM_MODE,t,e)},init:function(t,e,n){this.cfg=this.cfg.extend(n),this._xformMode=t,this._key=e,this.reset()},reset:function(){r.reset.call(this),this._doReset()},process:function(t){return this._append(t),this._process()},finalize:function(t){return t&&this._append(t),this._doFinalize()},keySize:4,ivSize:4,_ENC_XFORM_MODE:1,_DEC_XFORM_MODE:2,_createHelper:function(){function t(t){return"string"==typeof t?m:y}return function(e){return{encrypt:function(n,i,o){return t(i).encrypt(e,n,i,o)},decrypt:function(n,i,o){return t(i).decrypt(e,n,i,o)}}}}()});n.StreamCipher=l.extend({_doFinalize:function(){return this._process(!0)},blockSize:1});var d=e.mode={},h=n.BlockCipherMode=i.extend({createEncryptor:function(t,e){return this.Encryptor.create(t,e)},createDecryptor:function(t,e){return this.Decryptor.create(t,e)},init:function(t,e){this._cipher=t,this._iv=e}}),f=d.CBC=function(){var e=h.extend();function n(e,n,i){var o,r=this._iv;r?(o=r,this._iv=t):o=this._prevBlock;for(var a=0;a<i;a++)e[n+a]^=o[a]}return e.Encryptor=e.extend({processBlock:function(t,e){var i=this._cipher,o=i.blockSize;n.call(this,t,e,o),i.encryptBlock(t,e),this._prevBlock=t.slice(e,e+o)}}),e.Decryptor=e.extend({processBlock:function(t,e){var i=this._cipher,o=i.blockSize,r=t.slice(e,e+o);i.decryptBlock(t,e),n.call(this,t,e,o),this._prevBlock=r}}),e}(),p=(e.pad={}).Pkcs7={pad:function(t,e){for(var n=4*e,i=n-t.sigBytes%n,r=i<<24|i<<16|i<<8|i,a=[],s=0;s<i;s+=4)a.push(r);var c=o.create(a,i);t.concat(c)},unpad:function(t){var e=255&t.words[t.sigBytes-1>>>2];t.sigBytes-=e}};n.BlockCipher=l.extend({cfg:l.cfg.extend({mode:f,padding:p}),reset:function(){var t;l.reset.call(this);var e=this.cfg,n=e.iv,i=e.mode;this._xformMode==this._ENC_XFORM_MODE?t=i.createEncryptor:(t=i.createDecryptor,this._minBufferSize=1),this._mode&&this._mode.__creator==t?this._mode.init(this,n&&n.words):(this._mode=t.call(i,this,n&&n.words),this._mode.__creator=t)},_doProcessBlock:function(t,e){this._mode.processBlock(t,e)},_doFinalize:function(){var t,e=this.cfg.padding;return this._xformMode==this._ENC_XFORM_MODE?(e.pad(this._data,this.blockSize),t=this._process(!0)):(t=this._process(!0),e.unpad(t)),t},blockSize:4});var v=n.CipherParams=i.extend({init:function(t){this.mixIn(t)},toString:function(t){return(t||this.formatter).stringify(this)}}),g=(e.format={}).OpenSSL={stringify:function(t){var e=t.ciphertext,n=t.salt;return(n?o.create([1398893684,1701076831]).concat(n).concat(e):e).toString(s)},parse:function(t){var e,n=s.parse(t),i=n.words;return 1398893684==i[0]&&1701076831==i[1]&&(e=o.create(i.slice(2,4)),i.splice(0,4),n.sigBytes-=16),v.create({ciphertext:n,salt:e})}},y=n.SerializableCipher=i.extend({cfg:i.extend({format:g}),encrypt:function(t,e,n,i){i=this.cfg.extend(i);var o=t.createEncryptor(n,i),r=o.finalize(e),a=o.cfg;return v.create({ciphertext:r,key:n,iv:a.iv,algorithm:t,mode:a.mode,padding:a.padding,blockSize:t.blockSize,formatter:i.format})},decrypt:function(t,e,n,i){return i=this.cfg.extend(i),e=this._parse(e,i.format),t.createDecryptor(n,i).finalize(e.ciphertext)},_parse:function(t,e){return"string"==typeof t?e.parse(t,this):t}}),w=(e.kdf={}).OpenSSL={execute:function(t,e,n,i){i||(i=o.random(8));var r=c.create({keySize:e+n}).compute(t,i),a=o.create(r.words.slice(e),4*n);return r.sigBytes=4*e,v.create({key:r,iv:a,salt:i})}},m=n.PasswordBasedCipher=y.extend({cfg:y.cfg.extend({kdf:w}),encrypt:function(t,e,n,i){var o=(i=this.cfg.extend(i)).kdf.execute(n,t.keySize,t.ivSize);i.iv=o.iv;var r=y.encrypt.call(this,t,e,o.key,i);return r.mixIn(o),r},decrypt:function(t,e,n,i){i=this.cfg.extend(i),e=this._parse(e,i.format);var o=i.kdf.execute(n,t.keySize,t.ivSize,e.salt);return i.iv=o.iv,y.decrypt.call(this,t,e,o.key,i)}})}(),u.mode.CFB=function(){var t=u.lib.BlockCipherMode.extend();function e(t,e,n,i){var o,r=this._iv;r?(o=r.slice(0),this._iv=void 0):o=this._prevBlock,i.encryptBlock(o,0);for(var a=0;a<n;a++)t[e+a]^=o[a]}return t.Encryptor=t.extend({processBlock:function(t,n){var i=this._cipher,o=i.blockSize;e.call(this,t,n,o,i),this._prevBlock=t.slice(n,n+o)}}),t.Decryptor=t.extend({processBlock:function(t,n){var i=this._cipher,o=i.blockSize,r=t.slice(n,n+o);e.call(this,t,n,o,i),this._prevBlock=r}}),t}(),u.mode.ECB=((n=u.lib.BlockCipherMode.extend()).Encryptor=n.extend({processBlock:function(t,e){this._cipher.encryptBlock(t,e)}}),n.Decryptor=n.extend({processBlock:function(t,e){this._cipher.decryptBlock(t,e)}}),n),u.pad.AnsiX923={pad:function(t,e){var n=t.sigBytes,i=4*e,o=i-n%i,r=n+o-1;t.clamp(),t.words[r>>>2]|=o<<24-r%4*8,t.sigBytes+=o},unpad:function(t){var e=255&t.words[t.sigBytes-1>>>2];t.sigBytes-=e}},u.pad.Iso10126={pad:function(t,e){var n=4*e,i=n-t.sigBytes%n;t.concat(u.lib.WordArray.random(i-1)).concat(u.lib.WordArray.create([i<<24],1))},unpad:function(t){var e=255&t.words[t.sigBytes-1>>>2];t.sigBytes-=e}},u.pad.Iso97971={pad:function(t,e){t.concat(u.lib.WordArray.create([2147483648],1)),u.pad.ZeroPadding.pad(t,e)},unpad:function(t){u.pad.ZeroPadding.unpad(t),t.sigBytes--}},u.mode.OFB=(t=u.lib.BlockCipherMode.extend(),e=t.Encryptor=t.extend({processBlock:function(t,e){var n=this._cipher,i=n.blockSize,o=this._iv,r=this._keystream;o&&(r=this._keystream=o.slice(0),this._iv=void 0),n.encryptBlock(r,0);for(var a=0;a<i;a++)t[e+a]^=r[a]}}),t.Decryptor=e,t),u.pad.NoPadding={pad:function(){},unpad:function(){}},function(t){var e=u,n=e.lib.CipherParams,i=e.enc.Hex;e.format.Hex={stringify:function(t){return t.ciphertext.toString(i)},parse:function(t){var e=i.parse(t);return n.create({ciphertext:e})}}}(),function(){var t=u,e=t.lib.BlockCipher,n=t.algo,i=[],o=[],r=[],a=[],s=[],c=[],l=[],d=[],h=[],f=[];!function(){for(var t=[],e=0;e<256;e++)t[e]=e<128?e<<1:e<<1^283;var n=0,u=0;for(e=0;e<256;e++){var p=u^u<<1^u<<2^u<<3^u<<4;p=p>>>8^255&p^99,i[n]=p,o[p]=n;var v=t[n],g=t[v],y=t[g],w=257*t[p]^16843008*p;r[n]=w<<24|w>>>8,a[n]=w<<16|w>>>16,s[n]=w<<8|w>>>24,c[n]=w,w=16843009*y^65537*g^257*v^16843008*n,l[p]=w<<24|w>>>8,d[p]=w<<16|w>>>16,h[p]=w<<8|w>>>24,f[p]=w,n?(n=v^t[t[t[y^v]]],u^=t[t[u]]):n=u=1}}();var p=[0,1,2,4,8,16,32,64,128,27,54],v=n.AES=e.extend({_doReset:function(){if(!this._nRounds||this._keyPriorReset!==this._key){for(var t=this._keyPriorReset=this._key,e=t.words,n=t.sigBytes/4,o=4*((this._nRounds=n+6)+1),r=this._keySchedule=[],a=0;a<o;a++)a<n?r[a]=e[a]:(u=r[a-1],a%n?n>6&&a%n==4&&(u=i[u>>>24]<<24|i[u>>>16&255]<<16|i[u>>>8&255]<<8|i[255&u]):(u=i[(u=u<<8|u>>>24)>>>24]<<24|i[u>>>16&255]<<16|i[u>>>8&255]<<8|i[255&u],u^=p[a/n|0]<<24),r[a]=r[a-n]^u);for(var s=this._invKeySchedule=[],c=0;c<o;c++){if(a=o-c,c%4)var u=r[a];else u=r[a-4];s[c]=c<4||a<=4?u:l[i[u>>>24]]^d[i[u>>>16&255]]^h[i[u>>>8&255]]^f[i[255&u]]}}},encryptBlock:function(t,e){this._doCryptBlock(t,e,this._keySchedule,r,a,s,c,i)},decryptBlock:function(t,e){var n=t[e+1];t[e+1]=t[e+3],t[e+3]=n,this._doCryptBlock(t,e,this._invKeySchedule,l,d,h,f,o),n=t[e+1],t[e+1]=t[e+3],t[e+3]=n},_doCryptBlock:function(t,e,n,i,o,r,a,s){for(var c=this._nRounds,l=t[e]^n[0],d=t[e+1]^n[1],u=t[e+2]^n[2],h=t[e+3]^n[3],f=4,p=1;p<c;p++){var v=i[l>>>24]^o[d>>>16&255]^r[u>>>8&255]^a[255&h]^n[f++],g=i[d>>>24]^o[u>>>16&255]^r[h>>>8&255]^a[255&l]^n[f++],y=i[u>>>24]^o[h>>>16&255]^r[l>>>8&255]^a[255&d]^n[f++],w=i[h>>>24]^o[l>>>16&255]^r[d>>>8&255]^a[255&u]^n[f++];l=v,d=g,u=y,h=w}v=(s[l>>>24]<<24|s[d>>>16&255]<<16|s[u>>>8&255]<<8|s[255&h])^n[f++],g=(s[d>>>24]<<24|s[u>>>16&255]<<16|s[h>>>8&255]<<8|s[255&l])^n[f++],y=(s[u>>>24]<<24|s[h>>>16&255]<<16|s[l>>>8&255]<<8|s[255&d])^n[f++],w=(s[h>>>24]<<24|s[l>>>16&255]<<16|s[d>>>8&255]<<8|s[255&u])^n[f++],t[e]=v,t[e+1]=g,t[e+2]=y,t[e+3]=w},keySize:8});t.AES=e._createHelper(v)}(),function(){var t=u,e=t.lib,n=e.WordArray,i=e.BlockCipher,o=t.algo,r=[57,49,41,33,25,17,9,1,58,50,42,34,26,18,10,2,59,51,43,35,27,19,11,3,60,52,44,36,63,55,47,39,31,23,15,7,62,54,46,38,30,22,14,6,61,53,45,37,29,21,13,5,28,20,12,4],a=[14,17,11,24,1,5,3,28,15,6,21,10,23,19,12,4,26,8,16,7,27,20,13,2,41,52,31,37,47,55,30,40,51,45,33,48,44,49,39,56,34,53,46,42,50,36,29,32],s=[1,2,4,6,8,10,12,14,15,17,19,21,23,25,27,28],c=[{0:8421888,268435456:32768,536870912:8421378,805306368:2,1073741824:512,1342177280:8421890,1610612736:8389122,1879048192:8388608,2147483648:514,2415919104:8389120,2684354560:33280,2952790016:8421376,3221225472:32770,3489660928:8388610,3758096384:0,4026531840:33282,134217728:0,402653184:8421890,671088640:33282,939524096:32768,1207959552:8421888,1476395008:512,1744830464:8421378,2013265920:2,2281701376:8389120,2550136832:33280,2818572288:8421376,3087007744:8389122,3355443200:8388610,3623878656:32770,3892314112:514,4160749568:8388608,1:32768,268435457:2,536870913:8421888,805306369:8388608,1073741825:8421378,1342177281:33280,1610612737:512,1879048193:8389122,2147483649:8421890,2415919105:8421376,2684354561:8388610,2952790017:33282,3221225473:514,3489660929:8389120,3758096385:32770,4026531841:0,134217729:8421890,402653185:8421376,671088641:8388608,939524097:512,1207959553:32768,1476395009:8388610,1744830465:2,2013265921:33282,2281701377:32770,2550136833:8389122,2818572289:514,3087007745:8421888,3355443201:8389120,3623878657:0,3892314113:33280,4160749569:8421378},{0:1074282512,16777216:16384,33554432:524288,50331648:1074266128,67108864:1073741840,83886080:1074282496,100663296:1073758208,117440512:16,134217728:540672,150994944:1073758224,167772160:1073741824,184549376:540688,201326592:524304,218103808:0,234881024:16400,251658240:1074266112,8388608:1073758208,25165824:540688,41943040:16,58720256:1073758224,75497472:1074282512,92274688:1073741824,109051904:524288,125829120:1074266128,142606336:524304,159383552:0,176160768:16384,192937984:1074266112,209715200:1073741840,226492416:540672,243269632:1074282496,260046848:16400,268435456:0,285212672:1074266128,301989888:1073758224,318767104:1074282496,335544320:1074266112,352321536:16,369098752:540688,385875968:16384,402653184:16400,419430400:524288,436207616:524304,452984832:1073741840,469762048:540672,486539264:1073758208,503316480:1073741824,520093696:1074282512,276824064:540688,293601280:524288,310378496:1074266112,327155712:16384,343932928:1073758208,360710144:1074282512,377487360:16,394264576:1073741824,411041792:1074282496,427819008:1073741840,444596224:1073758224,461373440:524304,478150656:0,494927872:16400,511705088:1074266128,528482304:540672},{0:260,1048576:0,2097152:67109120,3145728:65796,4194304:65540,5242880:67108868,6291456:67174660,7340032:67174400,8388608:67108864,9437184:67174656,10485760:65792,11534336:67174404,12582912:67109124,13631488:65536,14680064:4,15728640:256,524288:67174656,1572864:67174404,2621440:0,3670016:67109120,4718592:67108868,5767168:65536,6815744:65540,7864320:260,8912896:4,9961472:256,11010048:67174400,12058624:65796,13107200:65792,14155776:67109124,15204352:67174660,16252928:67108864,16777216:67174656,17825792:65540,18874368:65536,19922944:67109120,20971520:256,22020096:67174660,23068672:67108868,24117248:0,25165824:67109124,26214400:67108864,27262976:4,28311552:65792,29360128:67174400,30408704:260,31457280:65796,32505856:67174404,17301504:67108864,18350080:260,19398656:67174656,20447232:0,21495808:65540,22544384:67109120,23592960:256,24641536:67174404,25690112:65536,26738688:67174660,27787264:65796,28835840:67108868,29884416:67109124,30932992:67174400,31981568:4,33030144:65792},{0:2151682048,65536:2147487808,131072:4198464,196608:2151677952,262144:0,327680:4198400,393216:2147483712,458752:4194368,524288:2147483648,589824:4194304,655360:64,720896:2147487744,786432:2151678016,851968:4160,917504:4096,983040:2151682112,32768:2147487808,98304:64,163840:2151678016,229376:2147487744,294912:4198400,360448:2151682112,425984:0,491520:2151677952,557056:4096,622592:2151682048,688128:4194304,753664:4160,819200:2147483648,884736:4194368,950272:4198464,1015808:2147483712,1048576:4194368,1114112:4198400,1179648:2147483712,1245184:0,1310720:4160,1376256:2151678016,1441792:2151682048,1507328:2147487808,1572864:2151682112,1638400:2147483648,1703936:2151677952,1769472:4198464,1835008:2147487744,1900544:4194304,1966080:64,2031616:4096,1081344:2151677952,1146880:2151682112,1212416:0,1277952:4198400,1343488:4194368,1409024:2147483648,1474560:2147487808,1540096:64,1605632:2147483712,1671168:4096,1736704:2147487744,1802240:2151678016,1867776:4160,1933312:2151682048,1998848:4194304,2064384:4198464},{0:128,4096:17039360,8192:262144,12288:536870912,16384:537133184,20480:16777344,24576:553648256,28672:262272,32768:16777216,36864:537133056,40960:536871040,45056:553910400,49152:553910272,53248:0,57344:17039488,61440:553648128,2048:17039488,6144:553648256,10240:128,14336:17039360,18432:262144,22528:537133184,26624:553910272,30720:536870912,34816:537133056,38912:0,43008:553910400,47104:16777344,51200:536871040,55296:553648128,59392:16777216,63488:262272,65536:262144,69632:128,73728:536870912,77824:553648256,81920:16777344,86016:553910272,90112:537133184,94208:16777216,98304:553910400,102400:553648128,106496:17039360,110592:537133056,114688:262272,118784:536871040,122880:0,126976:17039488,67584:553648256,71680:16777216,75776:17039360,79872:537133184,83968:536870912,88064:17039488,92160:128,96256:553910272,100352:262272,104448:553910400,108544:0,112640:553648128,116736:16777344,120832:262144,124928:537133056,129024:536871040},{0:268435464,256:8192,512:270532608,768:270540808,1024:268443648,1280:2097152,1536:2097160,1792:268435456,2048:0,2304:268443656,2560:2105344,2816:8,3072:270532616,3328:2105352,3584:8200,3840:270540800,128:270532608,384:270540808,640:8,896:2097152,1152:2105352,1408:268435464,1664:268443648,1920:8200,2176:2097160,2432:8192,2688:268443656,2944:270532616,3200:0,3456:270540800,3712:2105344,3968:268435456,4096:268443648,4352:270532616,4608:270540808,4864:8200,5120:2097152,5376:268435456,5632:268435464,5888:2105344,6144:2105352,6400:0,6656:8,6912:270532608,7168:8192,7424:268443656,7680:270540800,7936:2097160,4224:8,4480:2105344,4736:2097152,4992:268435464,5248:268443648,5504:8200,5760:270540808,6016:270532608,6272:270540800,6528:270532616,6784:8192,7040:2105352,7296:2097160,7552:0,7808:268435456,8064:268443656},{0:1048576,16:33555457,32:1024,48:1049601,64:34604033,80:0,96:1,112:34603009,128:33555456,144:1048577,160:33554433,176:34604032,192:34603008,208:1025,224:1049600,240:33554432,8:34603009,24:0,40:33555457,56:34604032,72:1048576,88:33554433,104:33554432,120:1025,136:1049601,152:33555456,168:34603008,184:1048577,200:1024,216:34604033,232:1,248:1049600,256:33554432,272:1048576,288:33555457,304:34603009,320:1048577,336:33555456,352:34604032,368:1049601,384:1025,400:34604033,416:1049600,432:1,448:0,464:34603008,480:33554433,496:1024,264:1049600,280:33555457,296:34603009,312:1,328:33554432,344:1048576,360:1025,376:34604032,392:33554433,408:34603008,424:0,440:34604033,456:1049601,472:1024,488:33555456,504:1048577},{0:134219808,1:131072,2:134217728,3:32,4:131104,5:134350880,6:134350848,7:2048,8:134348800,9:134219776,10:133120,11:134348832,12:2080,13:0,14:134217760,15:133152,2147483648:2048,2147483649:134350880,2147483650:134219808,2147483651:134217728,2147483652:134348800,2147483653:133120,2147483654:133152,2147483655:32,2147483656:134217760,2147483657:2080,2147483658:131104,2147483659:134350848,2147483660:0,2147483661:134348832,2147483662:134219776,2147483663:131072,16:133152,17:134350848,18:32,19:2048,20:134219776,21:134217760,22:134348832,23:131072,24:0,25:131104,26:134348800,27:134219808,28:134350880,29:133120,30:2080,31:134217728,2147483664:131072,2147483665:2048,2147483666:134348832,2147483667:133152,2147483668:32,2147483669:134348800,2147483670:134217728,2147483671:134219808,2147483672:134350880,2147483673:134217760,2147483674:134219776,2147483675:0,2147483676:133120,2147483677:2080,2147483678:131104,2147483679:134350848}],l=[4160749569,528482304,33030144,2064384,129024,8064,504,2147483679],d=o.DES=i.extend({_doReset:function(){for(var t=this._key.words,e=[],n=0;n<56;n++){var i=r[n]-1;e[n]=t[i>>>5]>>>31-i%32&1}for(var o=this._subKeys=[],c=0;c<16;c++){var l=o[c]=[],d=s[c];for(n=0;n<24;n++)l[n/6|0]|=e[(a[n]-1+d)%28]<<31-n%6,l[4+(n/6|0)]|=e[28+(a[n+24]-1+d)%28]<<31-n%6;for(l[0]=l[0]<<1|l[0]>>>31,n=1;n<7;n++)l[n]=l[n]>>>4*(n-1)+3;l[7]=l[7]<<5|l[7]>>>27}var u=this._invSubKeys=[];for(n=0;n<16;n++)u[n]=o[15-n]},encryptBlock:function(t,e){this._doCryptBlock(t,e,this._subKeys)},decryptBlock:function(t,e){this._doCryptBlock(t,e,this._invSubKeys)},_doCryptBlock:function(t,e,n){this._lBlock=t[e],this._rBlock=t[e+1],h.call(this,4,252645135),h.call(this,16,65535),f.call(this,2,858993459),f.call(this,8,16711935),h.call(this,1,1431655765);for(var i=0;i<16;i++){for(var o=n[i],r=this._lBlock,a=this._rBlock,s=0,d=0;d<8;d++)s|=c[d][((a^o[d])&l[d])>>>0];this._lBlock=a,this._rBlock=r^s}var u=this._lBlock;this._lBlock=this._rBlock,this._rBlock=u,h.call(this,1,1431655765),f.call(this,8,16711935),f.call(this,2,858993459),h.call(this,16,65535),h.call(this,4,252645135),t[e]=this._lBlock,t[e+1]=this._rBlock},keySize:2,ivSize:2,blockSize:2});function h(t,e){var n=(this._lBlock>>>t^this._rBlock)&e;this._rBlock^=n,this._lBlock^=n<<t}function f(t,e){var n=(this._rBlock>>>t^this._lBlock)&e;this._lBlock^=n,this._rBlock^=n<<t}t.DES=i._createHelper(d);var p=o.TripleDES=i.extend({_doReset:function(){var t=this._key.words;if(2!==t.length&&4!==t.length&&t.length<6)throw new Error("Invalid key length - 3DES requires the key length to be 64, 128, 192 or >192.");var e=t.slice(0,2),i=t.length<4?t.slice(0,2):t.slice(2,4),o=t.length<6?t.slice(0,2):t.slice(4,6);this._des1=d.createEncryptor(n.create(e)),this._des2=d.createEncryptor(n.create(i)),this._des3=d.createEncryptor(n.create(o))},encryptBlock:function(t,e){this._des1.encryptBlock(t,e),this._des2.decryptBlock(t,e),this._des3.encryptBlock(t,e)},decryptBlock:function(t,e){this._des3.decryptBlock(t,e),this._des2.encryptBlock(t,e),this._des1.decryptBlock(t,e)},keySize:6,ivSize:2,blockSize:2});t.TripleDES=i._createHelper(p)}(),function(){var t=u,e=t.lib.StreamCipher,n=t.algo,i=n.RC4=e.extend({_doReset:function(){for(var t=this._key,e=t.words,n=t.sigBytes,i=this._S=[],o=0;o<256;o++)i[o]=o;o=0;for(var r=0;o<256;o++){var a=o%n,s=e[a>>>2]>>>24-a%4*8&255;r=(r+i[o]+s)%256;var c=i[o];i[o]=i[r],i[r]=c}this._i=this._j=0},_doProcessBlock:function(t,e){t[e]^=o.call(this)},keySize:8,ivSize:0});function o(){for(var t=this._S,e=this._i,n=this._j,i=0,o=0;o<4;o++){n=(n+t[e=(e+1)%256])%256;var r=t[e];t[e]=t[n],t[n]=r,i|=t[(t[e]+t[n])%256]<<24-8*o}return this._i=e,this._j=n,i}t.RC4=e._createHelper(i);var r=n.RC4Drop=i.extend({cfg:i.cfg.extend({drop:192}),_doReset:function(){i._doReset.call(this);for(var t=this.cfg.drop;t>0;t--)o.call(this)}});t.RC4Drop=e._createHelper(r)}(),
/** @preserve
       * Counter block mode compatible with  Dr Brian Gladman fileenc.c
       * derived from CryptoJS.mode.CTR
       * <NAME_EMAIL>
       */
u.mode.CTRGladman=function(){var t=u.lib.BlockCipherMode.extend();function e(t){if(255==(t>>24&255)){var e=t>>16&255,n=t>>8&255,i=255&t;255===e?(e=0,255===n?(n=0,255===i?i=0:++i):++n):++e,t=0,t+=e<<16,t+=n<<8,t+=i}else t+=1<<24;return t}function n(t){return 0===(t[0]=e(t[0]))&&(t[1]=e(t[1])),t}var i=t.Encryptor=t.extend({processBlock:function(t,e){var i=this._cipher,o=i.blockSize,r=this._iv,a=this._counter;r&&(a=this._counter=r.slice(0),this._iv=void 0),n(a);var s=a.slice(0);i.encryptBlock(s,0);for(var c=0;c<o;c++)t[e+c]^=s[c]}});return t.Decryptor=i,t}(),function(){var t=u,e=t.lib.StreamCipher,n=t.algo,i=[],o=[],r=[],a=n.Rabbit=e.extend({_doReset:function(){for(var t=this._key.words,e=this.cfg.iv,n=0;n<4;n++)t[n]=16711935&(t[n]<<8|t[n]>>>24)|4278255360&(t[n]<<24|t[n]>>>8);var i=this._X=[t[0],t[3]<<16|t[2]>>>16,t[1],t[0]<<16|t[3]>>>16,t[2],t[1]<<16|t[0]>>>16,t[3],t[2]<<16|t[1]>>>16],o=this._C=[t[2]<<16|t[2]>>>16,4294901760&t[0]|65535&t[1],t[3]<<16|t[3]>>>16,4294901760&t[1]|65535&t[2],t[0]<<16|t[0]>>>16,4294901760&t[2]|65535&t[3],t[1]<<16|t[1]>>>16,4294901760&t[3]|65535&t[0]];for(this._b=0,n=0;n<4;n++)s.call(this);for(n=0;n<8;n++)o[n]^=i[n+4&7];if(e){var r=e.words,a=r[0],c=r[1],l=16711935&(a<<8|a>>>24)|4278255360&(a<<24|a>>>8),d=16711935&(c<<8|c>>>24)|4278255360&(c<<24|c>>>8),u=l>>>16|4294901760&d,h=d<<16|65535&l;for(o[0]^=l,o[1]^=u,o[2]^=d,o[3]^=h,o[4]^=l,o[5]^=u,o[6]^=d,o[7]^=h,n=0;n<4;n++)s.call(this)}},_doProcessBlock:function(t,e){var n=this._X;s.call(this),i[0]=n[0]^n[5]>>>16^n[3]<<16,i[1]=n[2]^n[7]>>>16^n[5]<<16,i[2]=n[4]^n[1]>>>16^n[7]<<16,i[3]=n[6]^n[3]>>>16^n[1]<<16;for(var o=0;o<4;o++)i[o]=16711935&(i[o]<<8|i[o]>>>24)|4278255360&(i[o]<<24|i[o]>>>8),t[e+o]^=i[o]},blockSize:4,ivSize:2});function s(){for(var t=this._X,e=this._C,n=0;n<8;n++)o[n]=e[n];for(e[0]=e[0]+1295307597+this._b|0,e[1]=e[1]+3545052371+(e[0]>>>0<o[0]>>>0?1:0)|0,e[2]=e[2]+886263092+(e[1]>>>0<o[1]>>>0?1:0)|0,e[3]=e[3]+1295307597+(e[2]>>>0<o[2]>>>0?1:0)|0,e[4]=e[4]+3545052371+(e[3]>>>0<o[3]>>>0?1:0)|0,e[5]=e[5]+886263092+(e[4]>>>0<o[4]>>>0?1:0)|0,e[6]=e[6]+1295307597+(e[5]>>>0<o[5]>>>0?1:0)|0,e[7]=e[7]+3545052371+(e[6]>>>0<o[6]>>>0?1:0)|0,this._b=e[7]>>>0<o[7]>>>0?1:0,n=0;n<8;n++){var i=t[n]+e[n],a=65535&i,s=i>>>16,c=((a*a>>>17)+a*s>>>15)+s*s,l=((4294901760&i)*i|0)+((65535&i)*i|0);r[n]=c^l}t[0]=r[0]+(r[7]<<16|r[7]>>>16)+(r[6]<<16|r[6]>>>16)|0,t[1]=r[1]+(r[0]<<8|r[0]>>>24)+r[7]|0,t[2]=r[2]+(r[1]<<16|r[1]>>>16)+(r[0]<<16|r[0]>>>16)|0,t[3]=r[3]+(r[2]<<8|r[2]>>>24)+r[1]|0,t[4]=r[4]+(r[3]<<16|r[3]>>>16)+(r[2]<<16|r[2]>>>16)|0,t[5]=r[5]+(r[4]<<8|r[4]>>>24)+r[3]|0,t[6]=r[6]+(r[5]<<16|r[5]>>>16)+(r[4]<<16|r[4]>>>16)|0,t[7]=r[7]+(r[6]<<8|r[6]>>>24)+r[5]|0}t.Rabbit=e._createHelper(a)}(),u.mode.CTR=function(){var t=u.lib.BlockCipherMode.extend(),e=t.Encryptor=t.extend({processBlock:function(t,e){var n=this._cipher,i=n.blockSize,o=this._iv,r=this._counter;o&&(r=this._counter=o.slice(0),this._iv=void 0);var a=r.slice(0);n.encryptBlock(a,0),r[i-1]=r[i-1]+1|0;for(var s=0;s<i;s++)t[e+s]^=a[s]}});return t.Decryptor=e,t}(),function(){var t=u,e=t.lib.StreamCipher,n=t.algo,i=[],o=[],r=[],a=n.RabbitLegacy=e.extend({_doReset:function(){var t=this._key.words,e=this.cfg.iv,n=this._X=[t[0],t[3]<<16|t[2]>>>16,t[1],t[0]<<16|t[3]>>>16,t[2],t[1]<<16|t[0]>>>16,t[3],t[2]<<16|t[1]>>>16],i=this._C=[t[2]<<16|t[2]>>>16,4294901760&t[0]|65535&t[1],t[3]<<16|t[3]>>>16,4294901760&t[1]|65535&t[2],t[0]<<16|t[0]>>>16,4294901760&t[2]|65535&t[3],t[1]<<16|t[1]>>>16,4294901760&t[3]|65535&t[0]];this._b=0;for(var o=0;o<4;o++)s.call(this);for(o=0;o<8;o++)i[o]^=n[o+4&7];if(e){var r=e.words,a=r[0],c=r[1],l=16711935&(a<<8|a>>>24)|4278255360&(a<<24|a>>>8),d=16711935&(c<<8|c>>>24)|4278255360&(c<<24|c>>>8),u=l>>>16|4294901760&d,h=d<<16|65535&l;for(i[0]^=l,i[1]^=u,i[2]^=d,i[3]^=h,i[4]^=l,i[5]^=u,i[6]^=d,i[7]^=h,o=0;o<4;o++)s.call(this)}},_doProcessBlock:function(t,e){var n=this._X;s.call(this),i[0]=n[0]^n[5]>>>16^n[3]<<16,i[1]=n[2]^n[7]>>>16^n[5]<<16,i[2]=n[4]^n[1]>>>16^n[7]<<16,i[3]=n[6]^n[3]>>>16^n[1]<<16;for(var o=0;o<4;o++)i[o]=16711935&(i[o]<<8|i[o]>>>24)|4278255360&(i[o]<<24|i[o]>>>8),t[e+o]^=i[o]},blockSize:4,ivSize:2});function s(){for(var t=this._X,e=this._C,n=0;n<8;n++)o[n]=e[n];for(e[0]=e[0]+1295307597+this._b|0,e[1]=e[1]+3545052371+(e[0]>>>0<o[0]>>>0?1:0)|0,e[2]=e[2]+886263092+(e[1]>>>0<o[1]>>>0?1:0)|0,e[3]=e[3]+1295307597+(e[2]>>>0<o[2]>>>0?1:0)|0,e[4]=e[4]+3545052371+(e[3]>>>0<o[3]>>>0?1:0)|0,e[5]=e[5]+886263092+(e[4]>>>0<o[4]>>>0?1:0)|0,e[6]=e[6]+1295307597+(e[5]>>>0<o[5]>>>0?1:0)|0,e[7]=e[7]+3545052371+(e[6]>>>0<o[6]>>>0?1:0)|0,this._b=e[7]>>>0<o[7]>>>0?1:0,n=0;n<8;n++){var i=t[n]+e[n],a=65535&i,s=i>>>16,c=((a*a>>>17)+a*s>>>15)+s*s,l=((4294901760&i)*i|0)+((65535&i)*i|0);r[n]=c^l}t[0]=r[0]+(r[7]<<16|r[7]>>>16)+(r[6]<<16|r[6]>>>16)|0,t[1]=r[1]+(r[0]<<8|r[0]>>>24)+r[7]|0,t[2]=r[2]+(r[1]<<16|r[1]>>>16)+(r[0]<<16|r[0]>>>16)|0,t[3]=r[3]+(r[2]<<8|r[2]>>>24)+r[1]|0,t[4]=r[4]+(r[3]<<16|r[3]>>>16)+(r[2]<<16|r[2]>>>16)|0,t[5]=r[5]+(r[4]<<8|r[4]>>>24)+r[3]|0,t[6]=r[6]+(r[5]<<16|r[5]>>>16)+(r[4]<<16|r[4]>>>16)|0,t[7]=r[7]+(r[6]<<8|r[6]>>>24)+r[5]|0}t.RabbitLegacy=e._createHelper(a)}(),u.pad.ZeroPadding={pad:function(t,e){var n=4*e;t.clamp(),t.sigBytes+=n-(t.sigBytes%n||n)},unpad:function(t){var e=t.words,n=t.sigBytes-1;for(n=t.sigBytes-1;n>=0;n--)if(e[n>>>2]>>>24-n%4*8&255){t.sigBytes=n+1;break}}},u}();var I="",N="",L="01";var T=Object.freeze({__proto__:null,encode:function(t){var e={sec:"",body:""};if(!t||"object"!==O(t))return e;var n=function(){var t=(new Date).getTime().toString()+Math.floor(900*Math.random()+100).toString(),e=N+t;return{key:CryptoJS.MD5(e).toString().toLowerCase().slice(8,24),sec:window.btoa(I+";"+t+";"+L)}}(),i=JSON.stringify(t),o=CryptoJS.enc.Utf8.parse(n.key),r=CryptoJS.AES.encrypt(i,o,{mode:CryptoJS.mode.ECB,padding:CryptoJS.pad.Pkcs7}).toString();return e.sec=n.sec,e.body=r,e},decode:function(t){if(!t||!t.sec||!t.body)return"";var e=CryptoJS.enc.Utf8.parse(function(t){var e="";if(!t)return e;var n=window.atob(t).split(";");if(n&&3===n.length){var i=N+n[1];e=CryptoJS.MD5(i).toString().toLowerCase().slice(8,24)}return e}(t.sec)),n=CryptoJS.AES.decrypt(t.body,e,{mode:CryptoJS.mode.ECB,padding:CryptoJS.pad.Pkcs7});return JSON.parse(n.toString(CryptoJS.enc.Utf8))},init:function(t,e,n){void 0===n&&(n="01"),I=t,N=e,L=n||"01"}});t.Aes=T,t.Alert=x,t.AliLog=P,t.ExternalLink=m,t.Toast=H,t.Type=r,t.change2ChannelUrl=p,t.closeWebView=f,t.configureShare=function(t){var e,n,i,o;t.img=t.img||g,-1===t.img.indexOf("http")&&console.warn("��鈭怠㦛����憿餅糓撣行�柾ttp��讛悅��蝵𤑳�𨅯㦛���");var r=d(),a=JSON.stringify(t);"android"===r?null===(e=window.HkAndroid)||void 0===e||e.initShare(a):"ios"===r?null===(o=null===(i=null===(n=window.webkit)||void 0===n?void 0:n.messageHandlers)||void 0===i?void 0:i.setShareButton)||void 0===o||o.postMessage(a):console.warn("getSystem()�𠳿��膶ndroid,銋罸�𦢓os,霂瑟��䰻navigator.userAgent�糓�炏蝚血�āetSystem隞���餉��")},t.getAppVersion=u,t.getContactList=function(){return new Promise((function(t){if(l.value){var e=d();window.addressBookStatus=function(e){if(!e){var n=JSON.stringify({contactlist:"contactlist"}),i=d();"android"===i?window.HkAndroid.requestContactPermission(n):"ios"===i&&window.webkit.messageHandlers.requestContactPermission.postMessage(n),t(null)}},window.addressBookList=function(e){t(e)};var n=JSON.stringify({rejected:"addressBookStatus",contactlist:"addressBookList"});"android"===e?window.HkAndroid.fetchContacts(n):"ios"===e&&window.webkit.messageHandlers.fetchContacts.postMessage(n)}else t(null)}))},t.getDeviceInfo=function(){return new Promise((function(t){var e,n,i,o;if(l.value)try{var r=d();window.getDevice=function(e){t(e)};var a=JSON.stringify({callbackName:"getDevice",needLogin:!0});"android"===r?null===(e=window.HkAndroid)||void 0===e||e.getDeviceInfoWithCallback(a):"ios"===r?null===(o=null===(i=null===(n=window.webkit)||void 0===n?void 0:n.messageHandlers)||void 0===i?void 0:i.getDeviceInfoWithCallback)||void 0===o||o.postMessage(a):console.warn("getSystem()�𠳿��膶ndroid,銋罸�𦢓os,霂瑟��䰻navigator.userAgent�糓�炏蝚血�āetSystem隞���餉��")}catch(e){console.error("getDeviceInfo error",e),t(!1)}else t(!1)}))},t.getLocation=function(){return new Promise((function(t){var e,n,i,o;if(l.value)try{var r=d();window.getLocationCallback=function(e){t(e)};var a=JSON.stringify({callbackName:"getLocationCallback"});"android"===r?null===(e=window.HkAndroid)||void 0===e||e.getLocation(a):"ios"===r&&(null===(o=null===(i=null===(n=window.webkit)||void 0===n?void 0:n.messageHandlers)||void 0===i?void 0:i.getLocation)||void 0===o||o.postMessage(a))}catch(e){console.error("getLocationCallback error",e),t({lat:"0",lng:"0"})}else t({lat:"0",lng:"0"})}))},t.getSystem=d,t.getUserInfo=function(t){return void 0===t&&(t=!0),new Promise((function(e){var n,i,o,r;if(l.value)try{var a=d();window.getUser=function(t){e(t)};var s=JSON.stringify({callbackName:"getUser",needLogin:t});"android"===a?null===(n=window.HkAndroid)||void 0===n||n.getBossInfoWithCallback(s):"ios"===a?null===(r=null===(o=null===(i=window.webkit)||void 0===i?void 0:i.messageHandlers)||void 0===o?void 0:o.getBossInfoWithCallback)||void 0===r||r.postMessage(s):console.warn("getSystem()�𠳿��膶ndroid,銋罸�𦢓os,霂瑟��䰻navigator.userAgent�糓�炏蝚血�āetSystem隞���餉��")}catch(t){console.error("getUserInfo error",t),e(!1)}else e(!1)}))},t.getVersionForAosAndIos=v,t.inApp=l,t.logEventStatistics=y,t.openApp=h,t.pageStatistics=w,t.saveImage=function(t){return s(this,void 0,void 0,(function(){var e,n;return c(this,(function(i){if(!t.startsWith("http"))return[2,!1];if(!l.value)return[2,!1];e=d(),n={imgUrl:t};try{return"android"===e?window.HkAndroid.savePhotoToAlbum(JSON.stringify(n)):"ios"===e&&window.webkit.messageHandlers.savePhotoToAlbum.postMessage(JSON.stringify(n)),[2,!0]}catch(t){return[2,!1]}return[2]}))}))},t.scan=function(){return new Promise((function(t){window.inviteCallback=function(e){return t(e)},window.location.href="openpage://30000?action=inviteCallback"}))},t.sendSms=function(t,e){if(l.value){var n=d(),i={mobileList:JSON.parse(JSON.stringify([t])),msg:e};"android"===n?window.HkAndroid.sendMessages(JSON.stringify(i)):"ios"===n&&window.webkit.messageHandlers.sendMessages.postMessage(JSON.stringify(i))}else console.warn("霂瑕銁app��雿輻鍂")},t.setAutoJumpApp=function(t,e){if(!l.value){for(var n=location.search.replace("?","").split("&"),i={},o=0;o<n.length;++o){var r=n[o].split("=");r[0]&&(i[r[0]]=r[1])}D((function(){if(e)null==e||e();else{var t=window.location.href.split("#")[0].split("?")[0],n=i,o="";Object.keys(n).forEach((function(t){"lang"!==t&&(o+="".concat(t,"=").concat(n[t],"&"))})),o&&(o=o.substring(0,o.length-1),t+="?".concat(o));var r,s=i.source,c=i.medium,l=i.campaign;if(s&&c&&l){var d=window.location.href.split("#")[0].split("?")[0],u=(r=a({},i),JSON.parse(JSON.stringify(r)));delete u.source,delete u.medium,delete u.campaign,u.lang="<<cmcchkhsh_cmplang>>",p(d,u,{source:s,medium:c,campaign:l})}else h(t,"openhkhshlogin")}}),t)}},t.setClickMask=D,t.setNavBtn=function(t,e){var n,i,o,r,a=d(),s=JSON.stringify({right1text:t,right1link:e});"android"===a?null===(n=window.HkAndroid)||void 0===n||n.setNavigationButtons(s):"ios"===a?null===(r=null===(o=null===(i=window.webkit)||void 0===i?void 0:i.messageHandlers)||void 0===o?void 0:o.setNavigationButtons)||void 0===r||r.postMessage(s):console.warn("getSystem()�𠳿��膶ndroid,銋罸�𦢓os,霂瑟��䰻navigator.userAgent�糓�炏蝚血�āetSystem隞���餉��")},t.shaking=function(){return new Promise((function(t){if(l.value){var e=d();window.startScanning=function(){t(!0)};var n=JSON.stringify({resolve:"startScanning"});"android"===e?window.HkAndroid.shakeStart(n):"ios"===e&&window.webkit.messageHandlers.shakeStart.postMessage(n)}}))},t.sharePic=function(t){var e,n,i,o,r=d(),a=JSON.stringify({imgUrl:t});"android"===r?null===(e=window.HkAndroid)||void 0===e||e.sharePicTo(a):"ios"===r?null===(o=null===(i=null===(n=window.webkit)||void 0===n?void 0:n.messageHandlers)||void 0===i?void 0:i.sharePicTo)||void 0===o||o.postMessage(a):console.warn("getSystem()�𠳿��膶ndroid,銋罸�𦢓os,霂瑟��䰻navigator.userAgent�糓�炏蝚血�āetSystem隞���餉��")},t.showHeaderRight=function(t){return s(void 0,void 0,void 0,(function(){var e,n,i,o;return c(this,(function(r){switch(r.label){case 0:return[4,v()];case 1:return r.sent()<1040?console.log("���𧋦��𣂼���雿�10.4.0"):(e=t||"cmcchkhsh://home","android"===(n=d())?null===window||void 0===window||window.HkAndroid.navRightIcons(e):"ios"===n&&(null===(o=null===(i=window.webkit)||void 0===i?void 0:i.messageHandlers)||void 0===o||o.navRightIcons.postMessage(e))),[2]}}))}))},t.stopScanning=function(){var t=d();"android"===t?window.HkAndroid.shakeEnd():"ios"===t&&window.webkit.messageHandlers.shakeEnd.postMessage(!0)},t.upShareControl=function(t){var e=t.url,n=t.title,i=t.content,o=t.img,r=t.type,a="cmcchkhsh://openshare?title=".concat(n,"&content=").concat(i,"&url=").concat(encodeURIComponent(e),"&img=").concat(encodeURIComponent(o||g));t.type&&(a+="&type=".concat(r)),window.location=a},Object.defineProperty(t,"__esModule",{value:!0})}));