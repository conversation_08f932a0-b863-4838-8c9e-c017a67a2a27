import { useAppStore } from '@/store'
import { useLang, useStorage } from '@/hook'
import { inApp } from '@via/mylink-sdk'
import router from '@/router'
import { type RouteLocationNormalizedGeneric } from 'vue-router'
const { lang } = useLang()
export function useGTM() {

    /**
     * 发送gtm数据
     * @param data 自定义埋点信息
     * @param _router 路由信息，可选参数，如果不传则默认使用useRoute()获取
     */
    const sendGTMData = (data: Record<string,any> = {}, _route?: RouteLocationNormalizedGeneric) => {
        try {

            const route = _route || router.currentRoute.value

            const appStore = useAppStore()

            const layerData = {
                "event": 'dle_pageview', // 每次页面加载时推送,时机需早于其他事件
                // "dlv_user_id": '', // 用户登录ID，获取不到时请不要包含该字段
                // "dlv_appclient_id": '', //Mylink ID ，获取不到时请不要包含该字段
                "dlv_interface_version": 'Standard Version', //用户使用的界面样式，如：Standard Version、Minimalist Version、Meta等
                "dlv_platform": appStore.isDecEnv ? 'H5' : (inApp.value ? 'MyLink' : 'H5'), // 页面被打开环境，如：MyLink APP、Eshop H5等
                "dlv_language": lang.value,//简体中文、繁体中文、English、等
                "dlv_page_name": route.name,//页面名称，如：homePage，分组名称
                "dlv_page_title": window.parent.document.title,// 页面标题
                "dlv_page_location": window.parent.location.href, //页面URL
                "dlv_is_login": `[${appStore.isLogin ? 'yes' : 'no'}]`,//"[yes||no]"是否登录
            }

            // 延时发送，主要是为了方便vconsole调试
            setTimeout(() => {
                console.info('gtm data', {
                    ...layerData,
                    ...data,
                })
            }, 1000);

            window.parent?.dataLayer?.push({
                ...layerData,
                ...data,
            });
        } catch (error) {
            console.error('sendGTMData error', error)
        }
    }

    return {
        sendGTMData
    }
}
