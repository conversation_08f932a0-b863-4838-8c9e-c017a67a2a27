export type ScrollElement = Element | Window

export function getScrollTop(el: ScrollElement): number {
    const top = 'scrollTop' in el ? el.scrollTop : el.pageYOffset

    // iOS scroll bounce cause minus scrollTop
    return Math.max(top, 0)
}

export function setScrollTop(el: ScrollElement, value: number) {
    if ('scrollTop' in el) {
        el.scrollTop = value
    } else {
        el.scrollTo(el.scrollX, value)
    }
}

export function getRootScrollTop(): number {
    return window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop || 0
}

const isWindow = (val: unknown): val is Window => val === window

const makeDOMRect = (width: number, height: number) =>
    ({
        top: 0,
        left: 0,
        right: width,
        bottom: height,
        width,
        height
    } as DOMRect)

export const useRect = (ele: Element | Window) => {
    if (isWindow(ele)) {
        const width = ele.innerWidth
        const height = ele.innerHeight
        return makeDOMRect(width, height)
    }

    if (ele?.getBoundingClientRect) {
        return ele.getBoundingClientRect()
    }

    return makeDOMRect(0, 0)
}
// get distance from element top to page top or scroller top
export function getElementTop(el: ScrollElement, scroller?: ScrollElement) {
    if (el === window) {
        return 0
    }

    const scrollTop = scroller ? getScrollTop(scroller) : getRootScrollTop()
    return useRect(el).top + scrollTop
}
