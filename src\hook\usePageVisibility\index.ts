import { useEventBus, EventBus<PERSON><PERSON> } from '../useEventBus'
import { useEventListener } from '../useEventListener'
import { reactive } from 'vue'

const { emit } = useEventBus()

let data = reactive({
  isHidden: false
})

let hiddenProperty: string | null = null
if ('hidden' in document) {
  hiddenProperty = 'hidden'
} else if ('webkitHidden' in document) {
  hiddenProperty = 'webkitHidden'
} else if ('mozHidden' in document) {
  hiddenProperty = 'mozHidden'
}

if (hiddenProperty) {
  const visibilityChangeEvent = hiddenProperty.replace(/hidden/i, 'visibilitychange')
  useEventListener(document, visibilityChangeEvent, (e) => {
    if (!hiddenProperty) {
      return
    }
    if (document[hiddenProperty]) {
      data.isHidden = true
      emit(EventBusKey.WEB_HIDDEN)
    } else {
      data.isHidden = false
      emit(EventBusKey.WEB_RESUME)
    }
  })
}

export function usePageVisibility() {
  return data
}
