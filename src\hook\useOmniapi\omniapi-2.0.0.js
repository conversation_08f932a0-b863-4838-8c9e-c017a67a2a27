/** OmniApi-SDK */
    const OMNI_API_SDK_VERSION = "2.0.0.002";

    /** 运行环境定义 */
    class OmniApiEnv {
        static PROD = "PROD";
        static UAT = "UAT";
    }

    /** SDK设置 */
    class OmniApiSdkSettings {
        static enableShowBuiltInPageInitError = true;
    }

    /** SDK常量 */
    class OmniApiConstant {
        //消息返回码定义
        static MessageCode = {
            SUCCESS: '000000'
        }

        static MessageDesc = {
            SDK_NOT_INIT: 'OmniApiSdk has not been initialized',
            PAGE_NOT_INIT: 'Page has not been initialized',
            HEADER_ERROR: 'get header error'
        }

        //默认语言
        static DEFAULT_LANGUAGE = "zh-HK";

        //domain地址定义
        static ENV_PROD_DOMAIN = "https://omniapi.hk.chinamobile.com";
        static ENV_UAT_DOMAIN = "https://omniapi-uat.hk.chinamobile.com";

        //omniapi服务地址定义
        static omniApiServiceUrls = {
            getDeviceID: "/api/omni-channel-service-common/rest/single/getDeviceID",
            init: "/api/omni-channel-service-common/rest/access-flow/init",
            getEcosysParam: "/api/omni-channel-service-common/rest/omniapi-sdk/getEcosysParamForJsSdk"
        }

        //omniapi应用体系参数key定义
        static OMNI_API_SDK_VERSION = "omni-js-sdk-version";
        static ACCEPT_LANGUAGE = "Accept-Language";
        static ACCEPT_LANGUAGE_ALIAS = "Acc-Lang";
        static OMNI_REFERER = "omni-referer";
        static OMNI_CONTENT_TYPE = "omni-content-type";
        static OMNI_APP_TOKEN = "omni-app-token";
        static CMHK_CHANNEL = "cmhkChannel";
        static CHANNEL_ID = "channelId"; //@Deprecated
        static CMHK_TOKEN = "cmhkToken";
        static OMNI_TOKEN = "omniToken";
        static OMNI_CLIENT_DEVICE_ID = "omni-client-device-id";
        static OMNI_TRACE_ID = "omni-trace-id";
        static STAFF_ID = "staffId";
        static ORG_ID = "orgId";
        static SES_PARAMS = "sesParams";

        //ses_开头前缀
        static SES_PARAMS_PREFIX = "ses_";
        //当前窗口地址
        static LOCATION_CURRENT = "current";
        //顶级窗口地址
        static LOCATION_TOP = "top";

        //
        static INIT_ERROR_CODE_PREFIX = "8403";
    }

    class OmniApiXhr {
        xhr = new XMLHttpRequest();
        GET = "GET";
        POST = "POST";
        CONTENT_TYPE = "content-type";
        APPLICATION_JSON = "application/json";

        constructor() {
            this.xhr.timeout = 60000;
            this.xhr.withCredentials = true;
        }

        async requestInterceptor({url, method, payload, headers}) {
            console.info('requestInterceptor', url)
            return {url, method, payload, headers};
        }

        async responseInterceptor(responseText) {
            console.info('responseInterceptor')
            return responseText;
        }

        async executeXhr(url, method, payload, headers) {
            return new Promise(async (resolve, reject) => {
                const requestInterceptorReturn = await this.requestInterceptor({url, method, payload, headers});
                requestInterceptorReturn.url && (url = requestInterceptorReturn.url);
                requestInterceptorReturn.method && (method = requestInterceptorReturn.method);
                requestInterceptorReturn.payload && (payload = requestInterceptorReturn.payload);
                requestInterceptorReturn.headers && (headers = requestInterceptorReturn.headers);
                //
                this.xhr.open(method, url);
                //
                if(headers instanceof Map){
                    headers.forEach((value, key, map) => {
                        this.xhr.setRequestHeader(key, value);
                    });
                }
                //
                this.xhr.onreadystatechange = async () => {
                    if(this.xhr.readyState === this.xhr.DONE){
                        if(this.xhr.status === 200){
                            const headerMap = new Map;
                            // Get the raw header string
                            const headers = this.xhr.getAllResponseHeaders();
                            // Convert the header string into an array
                            // of individual headers
                            const headerArr = headers.trim().split(/[\r\n]+/);
                            // Create a map of header names to values
                            headerArr.forEach(function (line) {
                                let parts = line.split(': ');
                                let headerName = parts.shift();
                                let headerValue = parts.join(': ');
                                headerMap.set(headerName, headerValue);
                            });
                            //console.log(xhr.responseText);
                            const _responseText = await this.responseInterceptor(this.xhr.responseText);
                            resolve({responseBody: JSON.parse(_responseText), responseHeaders: headerMap});
                        }else{
                            //console.log(xhr.status);
                            reject("OmniApi XHR:" + url + "|status=" + this.xhr.status);
                        }
                    }
                };
                this.xhr.send(payload);
            });
        }

        async jsonGet(url, payload, headers) {
            if(!(headers instanceof Map)){
                headers = new Map();
            }
            headers.set(this.CONTENT_TYPE, this.APPLICATION_JSON);
            const _payload = JSON.stringify(payload);
            const response = await this.executeXhr(url, this.GET, _payload, headers);
            return response;
        }

        async jsonPost(url, payload, headers) {
            if(!(headers instanceof Map)){
                headers = new Map();
            }
            headers.set(this.CONTENT_TYPE, this.APPLICATION_JSON);
            const _payload = JSON.stringify(payload);
            const response = await this.executeXhr(url, this.POST, _payload, headers);
            return response;
        }

        async formPost(url, payload, headers) {
            if(!(headers instanceof Map)){
                headers = new Map();
            }
            const response = await this.executeXhr(url, this.POST, payload, headers);
            return response;
        }
    }

    /** sdk-util */
    class OmniApiUtil {
        //生成uuid
        static uuid = (len, radix) => {
            let chars = '0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz'.split('');
            let uuid = [],
                i;
            radix = radix || chars.length;

            if (len) {
                // Compact form
                for (i = 0; i < len; i++) uuid[i] = chars[0 | Math.random() * radix];
            } else {
                // rfc4122, version 4 form
                let r;

                // rfc4122 requires these characters
                uuid[8] = uuid[13] = uuid[18] = uuid[23] = '-';
                uuid[14] = '4';

                // Fill in random data.  At i==19 set the high bits of clock sequence as
                // per rfc4122, sec. 4.1.5
                for (i = 0; i < 36; i++) {
                    if (!uuid[i]) {
                        r = 0 | Math.random() * 16;
                        uuid[i] = chars[(i == 19) ? (r & 0x3) | 0x8 : r];
                    }
                }
            }

            return uuid.join('');
        }

        /**
         * 判断是否是空的/不合规的值
         * @param value
         * @returns {boolean}
         */
        static isEmpty(value){
            if(value !== undefined && value !== "" && value != null && value !== "null"){
                return false;
            }
            return true;
        }

        /**
         * 检查URL中是否存在重复的参数，并返回详细的错误信息
         * @returns {Object} - {hasDuplicate: boolean, errorMessage: string}
         */
        static checkUrlAllDuplicateParam = () => {
            const params = new URL(location).searchParams;
            // 获取所有不重复的键
            const keys = [...new Set(params.keys())];

            for(let i = 0; i < keys.length; i++) {
                const { hasDuplicate, errorMessage } = OmniApiUtil.checkDuplicateParam(keys[i]);
                if (hasDuplicate) {
                    return { hasDuplicate, errorMessage }
                }
            }

            try {
                const top_loc_params = new URL(top.location).searchParams;
                // 获取所有不重复的键
                const top_loc_keys = [...new Set(top_loc_params.keys())];

                for(let i = 0; i < top_loc_keys.length; i++) {
                    const { hasDuplicate, errorMessage } = OmniApiUtil.checkDuplicateParam(top_loc_keys[i]);
                    if (hasDuplicate) {
                        return { hasDuplicate, errorMessage }
                    }
                }
            } catch (e) {
                // 忽略
            }

            return {
                hasDuplicate: false,
                errorMessage: ``
            };
        }

        /**
         * 检查URL中是否存在重复的参数，并返回详细的错误信息
         * @param {string} paramName - 参数名
         * @returns {Object} - {hasDuplicate: boolean, errorMessage: string}
         */
        static checkDuplicateParam = (paramName) => {
            try {
                let errorDetail = "";
                
                // 检查当前窗口URL
                const currentUrlStr = document.location.href;
                const currentRegex = new RegExp(`[?&]${paramName}=`, 'g');
                const currentMatches = currentUrlStr.match(currentRegex);
                
                if (currentMatches && currentMatches.length > 1) {
                    errorDetail = `在当前窗口URL中发现${currentMatches.length}个${paramName}参数`;
                    return {
                        hasDuplicate: true,
                        errorMessage: `参数重复错误: ${errorDetail}`
                    };
                }
                
                // 尝试检查顶级窗口URL
                try {
                    if (window.top !== window) {
                        const topUrlStr = top.document.location.href;
                        const topRegex = new RegExp(`[?&]${paramName}=`, 'g');
                        const topMatches = topUrlStr.match(topRegex);
                        
                        if (topMatches && topMatches.length > 1) {
                            errorDetail = `在顶级窗口URL中发现${topMatches.length}个${paramName}参数`;
                            return {
                                hasDuplicate: true,
                                errorMessage: `参数重复错误: ${errorDetail}`
                            };
                        }
                        
                        // 如果参数同时出现在当前窗口和顶级窗口，也视为重复
                        if (currentMatches && currentMatches.length > 0 && topMatches && topMatches.length > 0) {
                            errorDetail = `${paramName}参数同时出现在当前窗口和顶级窗口URL中`;
                            return {
                                hasDuplicate: true,
                                errorMessage: `参数重复错误: ${errorDetail}`
                            };
                        }
                    }
                } catch (e) {
                    // 跨域错误，忽略顶级窗口检查
                    console.warn(`无法检查顶级窗口的参数重复: ${e.message}`);
                }
                
                return {
                    hasDuplicate: false,
                    errorMessage: ""
                };
            } catch (e) {
                console.error(`检查参数重复时发生错误: ${e.message}`);
                return {
                    hasDuplicate: false,
                    errorMessage: ""
                };
            }
        }

        /**
         * 查找生态参数
         * @param paramName
         * @param winLocationNameArray
         * @param wujieSearchObjectArray
         * @param browserStorage
         * @returns {string}
         */
        static findEcosysParam = (paramName, winLocationNameArray, wujieSearchObjectArray, browserStorage) => {
            let _paramValue = "";
            console.info("findEcosysParam")
            // 从URL搜索参数中获取值
            const getParamFromUrl = (paramName, url) => {
                try {
                    const { hasDuplicate, errorMessage } = OmniApiUtil.checkDuplicateParam(paramName);
                    if (hasDuplicate) {
                        OmniApiCore.showBuiltInPageInitErrorIfEnabled(errorMessage);
                        return null;
                    }
                    
                    const urlSearchParams = new URL(url).searchParams;
                    const val = urlSearchParams.get(paramName);
                    return !OmniApiUtil.isEmpty(val) ? val : null;
                } catch (e) {
                    return null;
                }
            };

            // 尝试从当前窗口和顶级窗口获取参数
            for (let i = 0; i < winLocationNameArray.length; i++) {
                if (winLocationNameArray[i] === OmniApiConstant.LOCATION_CURRENT) {
                    const val = getParamFromUrl(paramName, document.location);
                    if (val) {
                        _paramValue = val;
                        break;
                    }
                } else if (winLocationNameArray[i] === OmniApiConstant.LOCATION_TOP) {
                    try {
                        const val = getParamFromUrl(paramName, top.document.location);
                        if (val) {
                            _paramValue = val;
                            break;
                        }
                    } catch (e) {
                        // 跨域访问错误，忽略
                    }
                }
            }
            
            // 如果在URL中未找到参数，尝试从wujie对象获取
            if (OmniApiUtil.isEmpty(_paramValue) && window.__POWERED_BY_WUJIE__) {
                for (let i = 0; i < wujieSearchObjectArray.length; i++) {
                    const item = wujieSearchObjectArray[i];
                    if (typeof item === 'object' && item && !OmniApiUtil.isEmpty(item[paramName])) {
                        _paramValue = item[paramName];
                        break;
                    }
                }
            }
            
            // 如果还未找到参数，尝试从存储中获取
            if (OmniApiUtil.isEmpty(_paramValue)) {
                _paramValue = browserStorage.getItem(paramName);
            }
            
            // 返回最终结果
            return OmniApiUtil.isEmpty(_paramValue) ? "" : _paramValue;
        }

        /**
         * 保存输入的参数
         * @param {*} paramName key
         * @param {*} paramValue value
         * @param {*} browserStorage localStorage/sessionStorage
         */
        static store = (paramName, paramValue, browserStorage) => {
            browserStorage.setItem(paramName, paramValue);
        }

        /**
         * 获取cmhkChannel参数值
         * @returns {string} cmhkChannel参数值
         */
        static retrieveCmhkChannel = () => {
            let winLocationNameArray= [OmniApiConstant.LOCATION_CURRENT];
            let currentLocationHostname = new URL(location).hostname;
            let topLocationHostname = "";
            try {
                topLocationHostname = new URL(top.location).hostname;
                //console.info("LOCATION=", currentLocationHostname, topLocationHostname)
                if (currentLocationHostname === topLocationHostname) {
                    winLocationNameArray.push(OmniApiConstant.LOCATION_TOP);
                }
            } catch (e) {
                // 跨域访问错误，忽略
            }

            return OmniApiUtil.findEcosysParam(OmniApiConstant.CMHK_CHANNEL
                , winLocationNameArray
                , [window.$wujie?.props?.data, window.$wujie?.props?.data?.busiData]
                , sessionStorage);
        }

        /**
         * 保存cmhkChannel参数值
         * @param {string} value - 要保存的值
         */
        static storeCmhkChannel = (value) => {
            OmniApiUtil.store(OmniApiConstant.CMHK_CHANNEL, value, sessionStorage);
        }

        /**
         * 获取语言
         * @returns
         */
        static getAcceptLanguage = () => {
            try {
                const { pathname } = new URL(window.location);
                const targetKeys = ['tc', 'en', 'sc'];
                const result = [];
                targetKeys.forEach(key => {
                    if (pathname.includes('/' + key + '/')) {
                        result.push(key);
                    }
                });

                targetKeys.forEach(key => {
                    if (pathname.endsWith('/' + key)) {
                        result.push(key);
                    }
                });

                const pathLang = result[0];

                if(!pathLang) {
                    return OmniApiConstant.DEFAULT_LANGUAGE;
                }

                switch (pathLang) {
                    case "tc":
                        return "zh-HK";
                    case "sc":
                        return "zh-CN";
                    case "en":
                        return "en-US";
                }
            } catch (error) {
                return OmniApiConstant.DEFAULT_LANGUAGE;
            }
        }

        /**
         * 获取OmniReferer
         * @returns
         */
        static getOmniReferer = () => {
            return location.href;
        }

        /**
         * 获取cmhkToken参数值
         * @returns {string} cmhkToken参数值
         */
        static retrieveCmhkToken = () => {
            return OmniApiUtil.findEcosysParam(OmniApiConstant.CMHK_TOKEN
                , [OmniApiConstant.LOCATION_CURRENT, OmniApiConstant.LOCATION_TOP]
                , [window.$wujie?.props?.data, window.$wujie?.props?.data?.busiData]
                , sessionStorage);
        }

        /**
         * 保存cmhkToken参数值
         * @param {string} value - 要保存的值
         */
        static storeCmhkToken = (value) => {
            OmniApiUtil.store(OmniApiConstant.CMHK_TOKEN, value, sessionStorage);
        }

        /**
         * 获取omniToken参数值
         * @returns {string} omniToken参数值
         */
        static retrieveOmniToken = () => {
            return OmniApiUtil.findEcosysParam(OmniApiConstant.OMNI_TOKEN
                , [OmniApiConstant.LOCATION_CURRENT, OmniApiConstant.LOCATION_TOP]
                , [window.$wujie?.props?.data, window.$wujie?.props?.data?.busiData]
                , sessionStorage);
        }

        /**
         * 保存omniToken参数值
         * @param {string} value - 要保存的值
         */
        static storeOmniToken = (value) => {
            OmniApiUtil.store(OmniApiConstant.OMNI_TOKEN, value, sessionStorage);
        }

        /**
         * 获取staffId参数值
         * @returns {string} staffId参数值
         */
        static retrieveStaffId = () => {
            return OmniApiUtil.findEcosysParam(OmniApiConstant.STAFF_ID
                , [OmniApiConstant.LOCATION_CURRENT, OmniApiConstant.LOCATION_TOP]
                , [window.$wujie?.props?.data, window.$wujie?.props?.data?.busiData]
                , sessionStorage);
        }

        /**
         * 保存staffId参数值
         * @param {string} value - 要保存的值
         */
        static storeStaffId = (value) => {
            OmniApiUtil.store(OmniApiConstant.STAFF_ID, value, sessionStorage);
        }

        /**
         * 获取orgId参数值
         * @returns {string} orgId参数值
         */
        static retrieveOrgId = () => {
            return OmniApiUtil.findEcosysParam(OmniApiConstant.ORG_ID
                , [OmniApiConstant.LOCATION_CURRENT, OmniApiConstant.LOCATION_TOP]
                , [window.$wujie?.props?.data, window.$wujie?.props?.data?.busiData]
                , sessionStorage);
        }

        /**
         * 保存orgId参数值
         * @param {string} value - 要保存的值
         */
        static storeOrgId = (value) => {
            OmniApiUtil.store(OmniApiConstant.ORG_ID, value, sessionStorage);
        }

        /**
         * 获取设备ID并保存
         * @param {*} headers
         * @returns
         */
        static getDeviceId = (headers) => {
            return new Promise(async (resolve, reject) => {
                let deviceId = localStorage.getItem(OmniApiConstant.OMNI_CLIENT_DEVICE_ID);
                if(OmniApiUtil.isEmpty(deviceId)){
                    let getDeviceIDResult = (await new OmniApiXhr().jsonPost(await OmniApiCore.getOmniApiDomain() + OmniApiConstant.omniApiServiceUrls.getDeviceID,{}, headers)).responseBody;
                    console.info('getDeviceIDResult', getDeviceIDResult);
                    if(getDeviceIDResult.code == OmniApiConstant.MessageCode.SUCCESS){
                        deviceId = getDeviceIDResult.data.deviceId;
                        //save
                        OmniApiUtil.store(OmniApiConstant.OMNI_CLIENT_DEVICE_ID, deviceId, localStorage);
                        //
                        resolve(deviceId);
                    }else{
                        if(getDeviceIDResult.code.startsWith(OmniApiConstant.INIT_ERROR_CODE_PREFIX)) {
                            OmniApiCore.showBuiltInPageInitErrorIfEnabled(getDeviceIDResult.message);
                        }
                        resolve("");
                    }
                }else{
                    resolve(deviceId);
                }
            });
        }

        /**
         * 获取以ses_为前缀的参数并保存
         * @returns
         */
        static findSesParamsAndStore = () => {
            let searchParams = new URL(document.location).searchParams;
            try {
                searchParams = new URL(top.document.location).searchParams;
            } catch (e) {
                //TODO
            }
            let sesParams = {};
            // collect key startsWith "ses_" param
            if(searchParams) {
                for (const [key, value] of searchParams.entries()) {
                    if (key.startsWith(OmniApiConstant.SES_PARAMS_PREFIX)) {
                        sesParams[key] = value;
                    }
                }
            }
            // 如果在URL中未找到参数，尝试从wujie对象获取
            if (window.__POWERED_BY_WUJIE__) {
                let wujieSearchObjectArray = [window.$wujie?.props?.data, window.$wujie?.props?.data?.busiData]
                for (let i = 0; i < wujieSearchObjectArray.length; i++) {
                    const item = wujieSearchObjectArray[i];
                    if (typeof item === 'object' && item) {
                        Object.keys(item).forEach(key => {
                            if (key.startsWith(OmniApiConstant.SES_PARAMS_PREFIX) && !sesParams[key] && item[key]) {
                                sesParams[key] = item[key];
                            }
                        })
                    }
                }
            }
            OmniApiUtil.store(OmniApiConstant.SES_PARAMS, JSON.stringify(sesParams), sessionStorage);
            return sesParams;
        }

        /**
         * 生成traceId
         * @param headers
         * @returns {Promise<unknown>}
         */
        static async genTraceId(headers){
            return new Promise(async (resolve, reject) => {
                const traceId = (await OmniApiUtil.getDeviceId(headers)).replace("OCD", "TRA") + OmniApiUtil.uuid(8);
                resolve(traceId);
            })
        }
    }

    /** sdk-core */
    class OmniApiCore {
        static omniApiXhrConfig;
        static sdkIsInited = false;
        static pageIsInited = false;
        static omniApiDomain;
        static omniAppToken;
        static cmhkChannel;

        /**
         * 校验是否完成sdk初始化
         * @returns {boolean}
         */
        static checkSdkInitStateReady = () => {
            if (OmniApiCore.sdkIsInited) {
                return true;
            }
            return false;
        }

        /**
         * 校验是否完成sdk&page初始化
         * @returns {boolean}
         */
        static checkPageInitStateReady = () => {
            if (OmniApiCore.sdkIsInited && OmniApiCore.pageIsInited) {
                return true;
            }
            return false;
        }

        /**
         * 获取omniapi domain
         * @returns
         */
        static getOmniApiDomain = async () => {
            return new Promise(async (resolve, reject) => {
                if(!OmniApiCore.checkSdkInitStateReady()){
                    reject(OmniApiConstant.MessageDesc.SDK_NOT_INIT);
                    return;
                }
                resolve(OmniApiCore.omniApiDomain);
            });
        }

        /**
         * 获取referer
         * @returns
         */
        static getFixedRefererByEnv = () => {
            if (window.__POWERED_BY_WUJIE__) {
                return window.$wujie.shadowRoot.baseURI;
            } else {
                return top.location.href;
            }
        }

        /**
         * sdk初始化
         * @param {*} env 运营环境
         * @param {*} omniAppToken 合作伙伴token
         * @returns
         */
        static sdkInit = (env, omniAppToken) => {
            //根据环境判断domain
            if(OmniApiEnv.PROD === env) {
                OmniApiCore.omniApiDomain = OmniApiConstant.ENV_PROD_DOMAIN;
            }else if(OmniApiEnv.UAT === env){
                OmniApiCore.omniApiDomain = OmniApiConstant.ENV_UAT_DOMAIN;
            }else{
                console.info('invalid env:' + env);
            }
            if(OmniApiUtil.isEmpty(omniAppToken)){
                console.info('invalid omniAppToken:' + omniAppToken);
            }
            OmniApiCore.omniAppToken = omniAppToken;
            const cmhkChannel = OmniApiUtil.retrieveCmhkChannel();
            OmniApiUtil.storeCmhkChannel(cmhkChannel);
            OmniApiCore.cmhkChannel = cmhkChannel;
            OmniApiCore.sdkIsInited = true;
            console.info('sdkInit process over')
            return {isOK: true};
        }

        /**
         * 获取omniapi应用体系请求头。体系参数获取的同时也保存。
         * @returns
         */
        static getHeaders = async () => {
            return new Promise(async (resolve, reject) => {
                if(!OmniApiCore.checkSdkInitStateReady()){
                    resolve({isOK:false,detail:OmniApiConstant.MessageDesc.SDK_NOT_INIT});
                    return;
                }
                let headers = new Map();
                headers.set(OmniApiConstant.OMNI_API_SDK_VERSION, OMNI_API_SDK_VERSION);
                headers.set(OmniApiConstant.OMNI_CONTENT_TYPE, "omni/api");
                headers.set(OmniApiConstant.OMNI_APP_TOKEN, OmniApiCore.omniAppToken);
                
                // OmniReferer 处理
                const omniReferer = OmniApiUtil.getOmniReferer();
                if (!OmniApiUtil.isEmpty(omniReferer)) {
                    headers.set(OmniApiConstant.OMNI_REFERER, omniReferer);
                }
                
                // AcceptLanguage 处理
                const acceptLanguage = OmniApiUtil.getAcceptLanguage();
                if (!OmniApiUtil.isEmpty(acceptLanguage)) {
                    headers.set(OmniApiConstant.ACCEPT_LANGUAGE, acceptLanguage);
                    headers.set(OmniApiConstant.ACCEPT_LANGUAGE_ALIAS, acceptLanguage);
                }
                
                // cmhkChannel 处理
                const cmhkChannel = OmniApiUtil.retrieveCmhkChannel();
                if (!OmniApiUtil.isEmpty(cmhkChannel)) {
                    OmniApiUtil.storeCmhkChannel(cmhkChannel);
                    headers.set(OmniApiConstant.CMHK_CHANNEL, cmhkChannel);
                    headers.set(OmniApiConstant.CHANNEL_ID, cmhkChannel); //@Deprecated
                }
                
                // cmhkToken 处理
                const cmhkToken = OmniApiUtil.retrieveCmhkToken();
                if (!OmniApiUtil.isEmpty(cmhkToken)) {
                    OmniApiUtil.storeCmhkToken(cmhkToken);
                    headers.set(OmniApiConstant.CMHK_TOKEN, cmhkToken);
                }
                
                // omniToken 处理
                const omniToken = OmniApiUtil.retrieveOmniToken();
                if (!OmniApiUtil.isEmpty(omniToken)) {
                    OmniApiUtil.storeOmniToken(omniToken);
                    headers.set(OmniApiConstant.OMNI_TOKEN, omniToken);
                }
                
                // staffId 处理
                const staffId = OmniApiUtil.retrieveStaffId();
                if (!OmniApiUtil.isEmpty(staffId)) {
                    OmniApiUtil.storeStaffId(staffId);
                    headers.set(OmniApiConstant.STAFF_ID, staffId);
                }
                
                // orgId 处理
                const orgId = OmniApiUtil.retrieveOrgId();
                if (!OmniApiUtil.isEmpty(orgId)) {
                    OmniApiUtil.storeOrgId(orgId);
                    headers.set(OmniApiConstant.ORG_ID, orgId);
                }
                
                // deviceId 处理
                const deviceId = await OmniApiUtil.getDeviceId(headers);
                if (!OmniApiUtil.isEmpty(deviceId)) {
                    headers.set(OmniApiConstant.OMNI_CLIENT_DEVICE_ID, deviceId);
                }
                
                // traceId 处理
                const traceId = await OmniApiUtil.genTraceId(headers);
                if (!OmniApiUtil.isEmpty(traceId)) {
                    headers.set(OmniApiConstant.OMNI_TRACE_ID, traceId);
                }
                
                resolve({isOK:true,detail:headers});
            });
        }

        /**
         * 页面初始化
         * @returns
         */
        static pageInit = async (params) => {
            return new Promise(async (resolve, reject) => {
                const { hasDuplicate, errorMessage } = OmniApiUtil.checkUrlAllDuplicateParam();
                if (hasDuplicate) {
                    OmniApiCore.showBuiltInPageInitErrorIfEnabled(errorMessage);
                    resolve({isOK: false, detail: ""});
                    return;
                }
                if(!OmniApiCore.checkSdkInitStateReady()){
                    OmniApiCore.showBuiltInPageInitErrorIfEnabled(OmniApiConstant.MessageDesc.SDK_NOT_INIT);
                    resolve({isOK:false,detail:OmniApiConstant.MessageDesc.SDK_NOT_INIT});
                }
                //cmhkChannel参数输入方式
                if(params) {
                    if(params.cmhkChannel) {
                        OmniApiUtil.storeCmhkChannel(params.cmhkChannel);
                    }
                }
                //get headers
                const {isOK, detail} = await OmniApiCore.getHeaders();
                if(!isOK){
                    OmniApiCore.showBuiltInPageInitErrorIfEnabled(detail);
                    resolve({isOK, detail});
                }
                const headers = detail;
                console.info("headers", headers);
                /*call init service*/
                const manParams = params ? Object.keys(params).reduce((result, key) => {
                    if (key !== OmniApiConstant.CMHK_CHANNEL) {//exclude cmhkChannel
                        result[key] = params[key];
                    }
                    return result;
                }, {}) : {};
                let sesParams = OmniApiUtil.findSesParamsAndStore();
                const initApiPayload = {...manParams, ...sesParams};
                const initResult = await new OmniApiXhr().jsonPost(OmniApiCore.omniApiDomain + OmniApiConstant.omniApiServiceUrls.init, initApiPayload, headers);
                const initResponseBody = initResult.responseBody;
                console.info('pageInitResult', initResult)
                const initIsOK = initResponseBody.code === OmniApiConstant.MessageCode.SUCCESS;
                if(!initIsOK) {
                    if(initResponseBody.code.startsWith(OmniApiConstant.INIT_ERROR_CODE_PREFIX)) {
                        OmniApiCore.showBuiltInPageInitErrorIfEnabled(initResponseBody.message);
                    }
                }
                if(initIsOK) {
                    OmniApiCore.pageIsInited = true;
                }
                resolve({isOK: initIsOK, detail: initResponseBody});
                console.info('pageInit process over')
            });
        }

        static showBuiltInPageInitErrorIfEnabled = (textContent) => {
            if(!OmniApiSdkSettings.enableShowBuiltInPageInitError) {
                return;
            }
            //
            document.body.innerHTML = "";
            //
            const errorOverlay = document.createElement('div');
            errorOverlay.className = "omniapi-sdk-errorOverlay";
            errorOverlay.style.position = 'fixed';
            errorOverlay.style.top = '0';
            errorOverlay.style.left = '0';
            errorOverlay.style.width = '100%';
            errorOverlay.style.height = '100%';
            errorOverlay.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
            errorOverlay.style.backdropFilter = 'blur(5px)';
            errorOverlay.style.display = 'flex';
            errorOverlay.style.justifyContent = 'center';
            errorOverlay.style.alignItems = 'center';
            errorOverlay.style.zIndex = '9999';
            // 创建SVG图标元素
            const ns = "http://www.w3.org/2000/svg";
            // 创建SVG元素
            let svg = document.createElementNS(ns, "svg");
            svg.setAttribute("width", "90%");
            svg.setAttribute("height", "250");
            svg.setAttribute("viewBox", "0 0 200 250");
            // 创建圆形元素
            let circle = document.createElementNS(ns, "circle");
            circle.setAttribute("cx", "100");
            circle.setAttribute("cy", "100");
            circle.setAttribute("r", "80");
            circle.setAttribute("stroke", "#000000");
            circle.setAttribute("stroke-width", "10");
            circle.setAttribute("fill", "none");
            // 创建斜杠元素
            let line = document.createElementNS(ns, "line");
            line.setAttribute("x1", "30");
            line.setAttribute("y1", "30");
            line.setAttribute("x2", "170");
            line.setAttribute("y2", "170");
            line.setAttribute("stroke", "#000000");
            line.setAttribute("stroke-width", "10");
            // 创建文字元素
            let text = document.createElementNS(ns, "text");
            text.setAttribute("x", "100"); // 文字起始点的x坐标
            text.setAttribute("y", "220"); // 文字起始点的y坐标
            text.setAttribute("font-family", "Arial, sans-serif");
            text.setAttribute("font-size", "24");
            text.setAttribute("text-anchor", "middle"); // 设置水平居中
            text.textContent = textContent;
            // 将圆形和斜杠添加到SVG
            svg.appendChild(circle);
            svg.appendChild(line);
            svg.appendChild(text);
            errorOverlay.appendChild(svg);
            document.body.appendChild(errorOverlay)
        }

        /**
         * 请求拦截器
         * @param interceptor
         * @returns {Promise<void>}
         */
        static requestXhrInterceptor = async (interceptor) => {
            if(!OmniApiCore.checkSdkInitStateReady()){
                throw new Error(OmniApiConstant.MessageDesc.SDK_NOT_INIT);
            }
            OmniApiCore.omniApiXhrConfig.requestInterceptor = interceptor;
        }

        /**
         * 响应拦截器
         * @param interceptor
         * @returns {Promise<void>}
         */
        static responseXhrInterceptor = async (interceptor) => {
            if(!OmniApiCore.checkSdkInitStateReady()){
                throw new Error(OmniApiConstant.MessageDesc.SDK_NOT_INIT);
            }
            OmniApiCore.omniApiXhrConfig.responseInterceptor = interceptor;
        }

        /**
         * JSON协议GET方法请求接口
         * @param url
         * @param payload
         * @param headers
         * @returns {Promise<any>}
         */
        static jsonGetXhr = async (url, headers) => {
            if(!OmniApiCore.checkPageInitStateReady()){
                throw new Error(OmniApiConstant.MessageDesc.PAGE_NOT_INIT);
            }
            if(!(headers instanceof Map)){
                headers = new Map();
            }
            const {isOK, detail} = await OmniApiCore.getHeaders();
            if(!isOK){
                OmniApiCore.showBuiltInPageInitErrorIfEnabled(OmniApiConstant.MessageDesc.HEADER_ERROR);
            }
            detail.forEach((value, key, map) => {
                headers.set(key, value);
            });
            const xhr = new OmniApiXhr();
            return (await xhr.jsonGet(url, {}, headers)).responseBody;
        }

        /**
         * JSON协议POST方法请求接口
         * @param url
         * @param payload
         * @param headers
         * @returns {Promise<any>}
         */
        static jsonPostXhr = async (url, payload, headers) => {
            if(!OmniApiCore.checkPageInitStateReady()){
                throw new Error(OmniApiConstant.MessageDesc.PAGE_NOT_INIT);
            }
            if(!(headers instanceof Map)){
                headers = new Map();
            }
            const {isOK, detail} = await OmniApiCore.getHeaders();
            if(!isOK){
                OmniApiCore.showBuiltInPageInitErrorIfEnabled(OmniApiConstant.MessageDesc.HEADER_ERROR);
            }
            detail.forEach((value, key, map) => {
                headers.set(key, value);
            });
            const xhr = new OmniApiXhr();
            return (await xhr.jsonPost(url, payload, headers)).responseBody;
        }

        /**
         * FORM-DATA协议POST方法请求接口
         * @param url
         * @param payload - FormData类型
         * @param headers
         * @returns {Promise<any>}
         */
        static formPostXhr = async (url, payload, headers) => {
            if(!OmniApiCore.checkPageInitStateReady()){
                throw new Error(OmniApiConstant.MessageDesc.PAGE_NOT_INIT);
            }
            if(!(headers instanceof Map)){
                headers = new Map();
            }
            const {isOK, detail} = await OmniApiCore.getHeaders();
            if(!isOK){
                OmniApiCore.showBuiltInPageInitErrorIfEnabled(OmniApiConstant.MessageDesc.HEADER_ERROR);
            }
            detail.forEach((value, key, map) => {
                headers.set(key, value);
            });
            const xhr = new OmniApiXhr();
            return (await xhr.formPost(url, payload, headers)).responseBody;
        }

        static getEcosysParam = async () => {
            if(!OmniApiCore.checkPageInitStateReady()){
                throw new Error(OmniApiConstant.MessageDesc.PAGE_NOT_INIT);
            }
            const {isOK, detail} = await OmniApiCore.getHeaders();
            if(!isOK){
                OmniApiCore.showBuiltInPageInitErrorIfEnabled(OmniApiConstant.MessageDesc.HEADER_ERROR);
            }
            const headers = detail;
            const xhr = new OmniApiXhr();
            const getEcosysParamResult = (await xhr.jsonPost(await OmniApiCore.omniApiDomain + OmniApiConstant.omniApiServiceUrls.getEcosysParam, {}, headers)).responseBody;
            if(getEcosysParamResult.code === OmniApiConstant.MessageCode.SUCCESS){
                return getEcosysParamResult.data;
            }
            return {};
        }
    }

    /** public OmniApiEnv */
    window.OmniApiEnv = OmniApiEnv;
    /** public OmniApi */
    window.OmniApi = {
        settings: OmniApiSdkSettings,
        sdkInit: OmniApiCore.sdkInit,
        pageInit: OmniApiCore.pageInit,
        jsonGetXhr: OmniApiCore.jsonGetXhr,
        jsonPostXhr: OmniApiCore.jsonPostXhr,
        formPostXhr: OmniApiCore.formPostXhr,
        getEcosysParam: OmniApiCore.getEcosysParam
    };
    export function useOmniapi() {
      return {
        OmniApiEnv,
        sdkInit: OmniApiCore.sdkInit,
        pageInit: OmniApiCore.pageInit,
        jsonGetXhr: OmniApiCore.jsonGetXhr,
        jsonPostXhr: OmniApiCore.jsonPostXhr,
        formPostXhr: OmniApiCore.formPostXhr,
        getEcosysParam: OmniApiCore.getEcosysParam
      }
    }