import dayjs from 'dayjs'
//一些通用方法，可选择性使用，亦可自己添加或删除
class utils {
  //生成随机数
  getUUID(len: number): string {
    len = len || 6
    len = parseInt(String(len), 10)
    len = isNaN(len) ? 6 : len
    let seed = '0123456789abcdefghijklmnopqrstubwxyzABCEDFGHIJKLMNOPQRSTUVWXYZ'
    let seedLen = seed.length - 1
    let uuid = ''
    while (len--) {
      uuid += seed[Math.round(Math.random() * seedLen)]
    }
    return uuid
  }
  // 深拷贝
  deepcopy(source: any) {
    if (!source) {
      return source
    }
    let sourceCopy: any = source instanceof Array ? [] : {}
    for (let item in source) {
      sourceCopy[item] = typeof source[item] === 'object' ? this.deepcopy(source[item]) : source[item]
    }
    return sourceCopy
  }
  //时间戳转时间
  timetrans(date: number): string {
    let newDate = new Date(date.toString().length !== 13 ? date * 1000 : date) //如果date为13位不需要乘1000
    let Y = newDate.getFullYear() + '-'
    let M = (newDate.getMonth() + 1 < 10 ? '0' + (newDate.getMonth() + 1) : newDate.getMonth() + 1) + '-'
    let D = (newDate.getDate() < 10 ? '0' + newDate.getDate() : newDate.getDate()) + ' '
    let h = (newDate.getHours() < 10 ? '0' + newDate.getHours() : newDate.getHours()) + ':'
    let m = (newDate.getMinutes() < 10 ? '0' + newDate.getMinutes() : newDate.getMinutes()) + ':'
    let s = newDate.getSeconds() < 10 ? '0' + newDate.getSeconds() : newDate.getSeconds()
    return Y + M + D + h + m + s
  }
  //时间格式化
  Format(date: Date, fmt: string): string {
    //author: meizz
    let o: Record<string, any> = {
      'M+': date.getMonth() + 1, //月份
      'd+': date.getDate(), //日
      'H+': date.getHours(), //小时
      'm+': date.getMinutes(), //分
      's+': date.getSeconds(), //秒
      'q+': Math.floor((date.getMonth() + 3) / 3), //季度
      'S': date.getMilliseconds() //毫秒
    }
    if (/(y+)/.test(fmt)) fmt = fmt.replace(RegExp.$1, (date.getFullYear() + '').substr(4 - RegExp.$1.length))
    for (let k in o)
      if (new RegExp('(' + k + ')').test(fmt))
        fmt = fmt.replace(RegExp.$1, RegExp.$1.length == 1 ? o[k] : ('00' + o[k]).substr(('' + o[k]).length))
    return fmt
  }
  // 设置cookie
  setCookie(key: string, value: string, t: number) {
    let oDate = new Date() //创建日期对象
    oDate.setDate(oDate.getDate() + t) //设置过期时间
    document.cookie = key + '=' + value + ';expires=' + oDate.toUTCString() //设置cookie的名称，数值，过期时间
  }
  // 获取cookie
  getCookie(key: string) {
    let arr1 = document.cookie.split('; ') //将cookie按“; ”分割，数组元素为： cookie名=cookie值
    for (let i = 0; i < arr1.length; i++) {
      //分割数组里的每个元素
      let arr2 = arr1[i].split('=') //按照“=”分割
      if (arr2[0] == key) {
        //如果数组的第一个元素等于给定的cookie名称
        return decodeURI(arr2[1]) //返回翻译编码后的cookie值
      }
    }
  }
  // 删除cookie
  removeCookie(key: string) {
    this.setCookie(key, '', -1) //cookie的过期时间设为昨天
  }
  //下载base64图片
  downloadFile(content: string, fileName: string) {
    let base64ToBlob = function (code: string) {
      let parts = code.split(';base64,')
      let contentType = parts[0].split(':')[1]
      let raw = window.atob(parts[1])
      let rawLength = raw.length
      let uInt8Array = new Uint8Array(rawLength)
      for (let i = 0; i < rawLength; ++i) {
        uInt8Array[i] = raw.charCodeAt(i)
      }
      return new Blob([uInt8Array], {
        type: contentType
      })
    }
    let aLink = document.createElement('a')
    let blob = base64ToBlob(content) //new Blob([content]);
    let evt = document.createEvent('HTMLEvents')
    evt.initEvent('click', true, true) //initEvent 不加后两个参数在FF下会报错  事件类型，是否冒泡，是否阻止浏览器的默认行为
    aLink.download = fileName
    aLink.href = URL.createObjectURL(blob)
    aLink.click()
  }
  //ajax错误处理
  catchError(error: Record<string, any>) {
    if (error.response) {
      switch (error.response.status) {
        case 400:
          // Vue.prototype.$Toast({
          //     msg:error.response.data.message || '请求参数异常'
          // })
          // Vue.prototype.$Message.warning({
          //     content: error.response.data.message || '请求参数异常'
          // });
          break
        case 401:
          // Vue.prototype.$Toast({
          //     msg:error.response.data.message || '密码错误或账号不存在！'
          // })
          // Vue.prototype.$Message.error({
          //     content: error.response.data.message || '密码错误或账号不存在！',
          //     onClose: function () {
          //         location.reload();
          //     }
          // });
          break
        case 403:
          // Vue.prototype.$Toast({
          //     msg:error.response.data.message || '无访问权限，请联系企业管理员'
          // })
          // Vue.prototype.$Message.warning({
          //     content: error.response.data.message || '无访问权限，请联系企业管理员',
          // });
          break
        default:
        // Vue.prototype.$Toast({
        //     msg:error.response.data.message || '服务端异常，请联系技术支持'
        // })
        // Vue.prototype.$networkError()
        // Vue.prototype.$Message.error({
        //     content: error.response.data.message || '服务端异常，请联系技术支持',
        // });
      }
    } else {
      // Vue.prototype.$Toast({
      //     msg: '网络错误,请稍后再试'
      // })
      // Vue.prototype.$networkError()
      // alert('网络错误')
      // Vue.prototype.$Message.error({
      //     content: '网络错误'
      // });
    }
    return Promise.reject(error)
  }

  /** 判断当前时间是否在某个时间之前 */
  isBefore(date: string | number | Date | dayjs.Dayjs | null | undefined) {
    return dayjs().isBefore(dayjs(date))
  }
  /** 判断当前时间是否在某个时间之后 */
  isAfter(date: string | number | Date | dayjs.Dayjs | null | undefined) {
    return dayjs().isAfter(dayjs(date))
  }
  /** 判断某个日期距离今天还剩多少毫秒数 */
  getLeftTime(date: string | number | Date | dayjs.Dayjs | null | undefined) {
    return dayjs(date).diff(dayjs())
  }
  /** 将天数和小时数合计为总小时数 */
  daySumHour(day: number, hour = 0) {
    return day * 24 + hour
  }
  /** 唯一值创建器 */
  onlyKeyCreator() {
    let str = Math.random().toString(36).substr(3)
    str += Date.now().toString(16).substr(4)
    return str
  }
  // 删除查询参数
  searchValueDel(name: string, href: string) {
    let loca = href || window.location.href
    let query = loca.split('#')[0].split('?')[1] || ''
    let isHash = ''
    if (loca.indexOf('#') > -1) {
      isHash = '#'
    } else {
      isHash = ''
    }
    if (!query) {
      return loca
    }
    if (loca.indexOf(name) > -1) {
      let obj: any = {}
      console.log('query', query)
      let arr: any = query.indexOf('&') > -1 ? query.split('&') : [query]
      for (let i = 0; i < arr.length; i++) {
        arr[i] = arr[i].split('=')
        obj[arr[i][0]] = arr[i][1]
      }
      delete obj[name]
      loca =
        loca.split('#')[0].split('?')[0] +
        (query && Object.keys(obj).length > 0 ? '?' : '') +
        JSON.stringify(obj)
          .replace(/[\"\{\}]/g, '')
          .replace(/\:/g, '=')
          .replace(/\,/g, '&') +
        isHash +
        (isHash ? loca.split('#')[1] : '')
      return loca
    } else {
      return loca
    }
  }
  // 获取地址栏参数-从hash读取
  hashValueDel(name: string, href: string) {
    let loca = href || window.location.href
    let query = ''
    if (loca.indexOf('#') > -1) {
      query = loca.split('#')[1].split('?')[1] || ''
      if (!query) {
        return loca
      }
    } else {
      return loca
    }
    if (loca.indexOf(name) > -1) {
      let obj: any = {}
      let arr: any = query.indexOf('&') > -1 ? query.split('&') : [query]
      for (let i = 0; i < arr.length; i++) {
        arr[i] = arr[i].split('=')
        obj[arr[i][0]] = arr[i][1]
      }
      delete obj[name]
      // console.log(343, loca.substring(0, loca.lastIndexOf('?') + 1))
      loca =
        loca.substring(0, loca.lastIndexOf('?')) +
        (query && Object.keys(obj).length > 0 ? '?' : '') +
        JSON.stringify(obj)
          .replace(/[\"\{\}]/g, '')
          .replace(/\:/g, '=')
          .replace(/\,/g, '&')
      return loca
    } else {
      return loca
    }
  }
  // 删除链接指定参数值
  delQueStr(name: any, href: any) {
    let newurl = this.hashValueDel(name, this.searchValueDel(name, href))
    return newurl
  }
  // 睡眠一段时间
  sleep(time: any) {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve(true)
      }, time || 1000)
    })
  }
  /** 导入某个目录所有的文件 */
  requireAll(context: { (arg0: any): any; keys: () => any[] }) {
    return context.keys().map((item: any) => {
      return {
        path: item,
        module: context(item)
      }
    })
  }
  //向当前url添加参数
  updateQuery(obj: Record<string, any>, hasHistory = true) {
    function updateQueryStringParameter(uri, key, value) {
      if (!value) {
        return uri
      }
      let re = new RegExp('([?&])' + key + '=.*?(&|$|#)', 'i')
      let separator = uri.indexOf('?') !== -1 ? '&' : '?'
      if (uri.match(re)) {
        return uri.replace(re, '$1' + key + '=' + value + '$2')
      } else {
        return uri + separator + key + '=' + value
      }
    }
    let newurl = location.href
    Object.keys(obj).forEach((key) => {
      newurl = updateQueryStringParameter(newurl, key, obj[key])
    })
    hasHistory
      ? window.history.pushState({ path: newurl }, '', newurl)
      : window.history.replaceState({ path: newurl }, '', newurl)
  }
  // 下载文件
  fileLinkToStreamDownload(url: string, fileName: string, type?: string) {
    let reg = /^([hH][tT]{2}[pP]:\/\/|[hH][tT]{2}[pP][sS]:\/\/)(([A-Za-z0-9-~]+).)+([A-Za-z0-9-~\/])+$/
    const ie = navigator.userAgent.match(/MSIE\s([\d.]+)/),
      ie11 = navigator.userAgent.match(/Trident\/7.0/) && navigator.userAgent.match(/rv:11/),
      ieEDGE = navigator.userAgent.match(/Edge/g),
      ieVer = ie ? ie[1] : ie11 ? 11 : ieEDGE ? 12 : -1
    console.log('ie:' + ie)
    console.log('ieVer:' + ieVer)
    if (ie && ieVer < 10) {
      throw new Error('No blobs on IE<10')
      return
    }
    if (!reg.test(url)) {
      throw new Error('传入参数不合法,不是标准的文件链接')
    } else {
      let xhr = new XMLHttpRequest()
      xhr.open('get', url, true)
      // xhr.setRequestHeader('Content-Type', `application/${type}`);
      xhr.responseType = 'blob'
      xhr.onload = function () {
        if (this.status == 200) {
          //接受二进制文件流
          console.log(this)
          let blob = this.response
          const blobUrl = window.URL.createObjectURL(blob)
          if (ieVer > -1) {
            ;(window.navigator as any).msSaveBlob(blob, fileName)
          } else {
            // 这里的文件名根据实际情况从响应头或者url里获取
            const a = document.createElement('a')
            a.href = blobUrl
            a.download = fileName
            a.click()
            window.URL.revokeObjectURL(blobUrl)
          }
        }
      }
      xhr.send()
    }
  }
  BlobToStreamDownload(blob: Blob, fileName: string) {
    const ie = navigator.userAgent.match(/MSIE\s([\d.]+)/),
      ie11 = navigator.userAgent.match(/Trident\/7.0/) && navigator.userAgent.match(/rv:11/),
      ieEDGE = navigator.userAgent.match(/Edge/g),
      ieVer = ie ? ie[1] : ie11 ? 11 : ieEDGE ? 12 : -1
    console.log('ie:' + ie)
    console.log('ieVer:' + ieVer)
    if (ie && ieVer < 10) {
      throw new Error('No blobs on IE<10')
      return
    }
    //接受二进制文件流
    const blobUrl = window.URL.createObjectURL(blob)
    if (ieVer > -1) {
      ;(window.navigator as any).msSaveBlob(blob, fileName)
    } else {
      // 这里的文件名根据实际情况从响应头或者url里获取
      const a = document.createElement('a')
      a.href = blobUrl
      a.download = fileName
      a.style.display = 'none'
      document.body.appendChild(a)
      a.click()
      window.URL.revokeObjectURL(blobUrl)
      document.body.removeChild(a)
    }
  }
  // 首字母大写
  titleCase(str: string) {
    let newStr = str.slice(0, 1).toUpperCase() + str.slice(1).toLowerCase()
    return newStr
  }
}

export default new utils()
