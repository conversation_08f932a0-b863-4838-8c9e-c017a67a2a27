import { Component, ComponentInternalInstance, createApp, getCurrentInstance, App } from 'vue'
import DialogApp from './DialogApp.vue'
import { state, rawState } from './state'
import registerList, { register } from './register'
import { Dialog } from './dialog'

export type DialogAlignX = 'left' | 'center' | 'right'
export type DialogAlignY = 'top' | 'center' | 'bottom'

export type DialogOpt = Partial<{
  maskClose: boolean // 是否可以点击背景触发关闭弹窗
  animName: string // 弹窗内容显示动画
  maskAnimName: string // 弹窗背景蒙版显示动画（黑色遮罩）
  maskBgColor: string // 弹窗蒙版背景色
  alignX: DialogAlignX
  alignY: DialogAlignY
}>

export type DialogData = {
  uuid: string
  name: string
  componentName: string
  props: Record<string, any>
  opts: DialogOpt
  isLocal: boolean // 是否局部弹窗
  isShowing: boolean
}

export type DialogRegisterType = Record<string, any> & {
  name: string
  dialogName?: string
  componentName?: string
  opts: DialogOpt
}

const dialogApp = createApp(DialogApp)
dialogApp.mount('#app-dialog')
register(dialogApp)

/**
 * 注册弹窗，显示弹窗之前，先要有注册，否则会读取不到弹窗配置
 * @param dialog
 */
function registerDialog(dialog: DialogRegisterType) {
  rawState.dialogMapper.set(dialog.dialogName || dialog.name, dialog)
}

registerList.forEach(registerDialog)

export function useDialog(
  localDialog?: Record<string, Component | { componentName?: string; component: Component; opts?: DialogOpt }>
) {
  // 先从当前组件获取，看是否有注册局部弹窗
  const parent = getCurrentInstance() || ({} as ComponentInternalInstance)

  // if (parent) {
  ;(parent as any).localDialog = localDialog
  // } else {
  // }
  // console.log(parent)

  return {
    registerDialog,
    app: dialogApp,
    state,
    rawState,
    get: (name: string, opts?: DialogOpt) => new Dialog(false, parent, name, opts),
    getSingle: (name: string, opts?: DialogOpt) => {
      const singleDialog = rawState.singleDialogMap.get(name)
      return singleDialog || new Dialog(true, parent, name, opts)
    },
    getInstance: (uuid: string) => rawState.dialogMap.get(uuid),
    closeAll: () => {
      while (state.showList.length) {
        const item = state.showList.pop() as DialogData
        rawState.dialogMap.get(item.uuid)?.close()
        rawState.singleDialogMap.get(item.name)?.close()
      }
    }
  }
}

const installDialog = (app: App) => {
  dialogApp._context.config.globalProperties = app._context.config.globalProperties
  dialogApp._context.components = app._context.components
}

export { installDialog }
