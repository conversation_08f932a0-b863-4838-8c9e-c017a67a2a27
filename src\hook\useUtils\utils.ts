import dayjs from 'dayjs'

const isObject = (obj) => Object.prototype.toString.call(obj) === "[object Object]";
class utils{
    // 获取session
    session(key:string, value:any):string|null|void {
        if (value === void(0)) {
            var lsVal = sessionStorage.getItem(key);
            if (lsVal && lsVal.indexOf('autostringify-') === 0) {
                return JSON.parse(lsVal.split('autostringify-')[1]);
            } else {
                return lsVal;
            }
        } else {
            if (typeof(value) === "object" || Array.isArray(value)) {
                value = 'autostringify-' + JSON.stringify(value);
            }
            ;
            return sessionStorage.setItem(key, value);
        }
    }
    //生成随机数
    getUUID(len:number):string {
        len = len || 6;
        len = parseInt(String(len), 10);
        len = isNaN(len) ? 6 : len;
        var seed = "0123456789abcdef<PERSON>ijklmnopqrstubwxyzABCEDFGHIJKLMNOPQRSTUVWXYZ";
        var seedLen = seed.length - 1;
        var uuid = "";
        while (len--) {
            uuid += seed[Math.round(Math.random() * seedLen)];
        }
        return uuid;
    }
    // 深拷贝
    deepcopy(source:any) {
        if (!source) {
            return source;
        }
        let sourceCopy:any = source instanceof Array ? [] : {};
        for (let item in source) {
            sourceCopy[item] = typeof source[item] === 'object' ? this.deepcopy(source[item]) : source[item];
        }
        return sourceCopy;
    }
    //时间戳转时间
    timetrans(date:number):string {
        var newDate = new Date(date.toString().length !== 13 ? date * 1000 : date);//如果date为13位不需要乘1000
        var Y = newDate.getFullYear() + '-';
        var M = (newDate.getMonth() + 1 < 10 ? '0' + (newDate.getMonth() + 1) : newDate.getMonth() + 1) + '-';
        var D = (newDate.getDate() < 10 ? '0' + (newDate.getDate()) : newDate.getDate()) + ' ';
        var h = (newDate.getHours() < 10 ? '0' + newDate.getHours() : newDate.getHours()) + ':';
        var m = (newDate.getMinutes() < 10 ? '0' + newDate.getMinutes() : newDate.getMinutes()) + ':';
        var s = (newDate.getSeconds() < 10 ? '0' + newDate.getSeconds() : newDate.getSeconds());
        return Y + M + D + h + m + s;
    }
    //时间格式化
    Format(date:Date,fmt:string):string { //author: meizz
        var o:Record<string,any> = {
            "M+": date.getMonth() + 1, //月份
            "d+": date.getDate(), //日
            "H+": date.getHours(), //小时
            "m+": date.getMinutes(), //分
            "s+": date.getSeconds(), //秒
            "q+": Math.floor((date.getMonth() + 3) / 3), //季度
            "S": date.getMilliseconds() //毫秒
        };
        if (/(y+)/.test(fmt)) fmt = fmt.replace(RegExp.$1, (date.getFullYear() + "").substr(4 - RegExp.$1.length));
        for (var k in o)
            if (new RegExp("(" + k + ")").test(fmt)) fmt = fmt.replace(RegExp.$1, (RegExp.$1.length == 1) ? (o[k]) : (("00" + o[k]).substr(("" + o[k]).length)));
        return fmt;
    }
    // 获取url参数
    getQueryString(name:string):string { 
        var reg:any = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i"); 
        var r = window.location.search.substr(1).match(reg); //获取url中"?"符后的字符串并正则匹配
        var context = ""; 
        if (r != null) 
           context = r[2]; 
        reg = null; 
        r = null; 
        return context == null || context == "" || context == "undefined" ? "" : context; 
    }
    getHashQueryString(name:any,url?:string){
        if(url){
            let Reg:any = (new RegExp('[?|&]' + name + '=' + '([^&;]+?)(&|#|;|$)').exec(url) || [, ""])
            return decodeURIComponent(Reg[1].replace(/\+/g, '%20')) || null
        }else{
            let Reg:any = (new RegExp('[?|&]' + name + '=' + '([^&;]+?)(&|#|;|$)').exec(location.href) || [, ""])
            return decodeURIComponent(Reg[1].replace(/\+/g, '%20')) || null
        }
    }
    // 设置cookie
    setCookie(key:string, value:string, t:number) {
        var oDate = new Date();  //创建日期对象
        oDate.setDate( oDate.getDate() + t ); //设置过期时间
        document.cookie = key + '=' + value + ';expires=' + oDate.toUTCString();  //设置cookie的名称，数值，过期时间
    }
    // 获取cookie
    getCookie(key:string) {
        var arr1 = document.cookie.split('; '); //将cookie按“; ”分割，数组元素为： cookie名=cookie值
        for (var i=0; i<arr1.length; i++) {  //分割数组里的每个元素
            var arr2 = arr1[i].split('='); //按照“=”分割
            if ( arr2[0] == key ) { //如果数组的第一个元素等于给定的cookie名称
                return decodeURI(arr2[1]);  //返回翻译编码后的cookie值
            }
        }
    }
    // 删除cookie
    removeCookie(key:string) {
        this.setCookie(key, '', -1);  //cookie的过期时间设为昨天
    }
	//下载base64图片
	downloadFile(content:string, fileName:string) {
		var base64ToBlob = function(code:string) {
			var parts = code.split(';base64,');
			var contentType = parts[0].split(':')[1];
			var raw = window.atob(parts[1]);
			var rawLength = raw.length;
			var uInt8Array = new Uint8Array(rawLength);
			for(var i = 0; i < rawLength; ++i) {
				uInt8Array[i] = raw.charCodeAt(i);
			}
			return new Blob([uInt8Array], {
				type: contentType
			});
		};
		var aLink = document.createElement('a');
		var blob = base64ToBlob(content); //new Blob([content]);
		var evt = document.createEvent("HTMLEvents");
		evt.initEvent("click", true, true); //initEvent 不加后两个参数在FF下会报错  事件类型，是否冒泡，是否阻止浏览器的默认行为
		aLink.download = fileName;
		aLink.href = URL.createObjectURL(blob);
		aLink.click();
    }
    //ajax错误处理
    catchError(error:Record<string,any>) {
        return Promise.reject(error);
    }
    // 公司名称与编码
    companyCodeAndName(code:number){
      let company:Record<number,string> = {
        1001: '广东移动',
        1002: '贵州移动',
        1003: '杭州研发中心',
        1004: '河北移动',
        1005: '河南移动',
        1006: '黑龙江移动',
        1007: '吉林移动',
        1008: '江苏移动',
        1009: '江西移动',
        1010: '辽宁移动',
        1011: '咪咕公司',
        1012: '内蒙古移动',
        1013: '宁夏移动',
        1014: '青海移动',
        1015: '山东移动',
        1016: '山西移动',
        1017: '陕西移动',
        1018: '上海产业研究院',
        1019: '上海移动',
        1020: '设计院',
        1021: '四川移动',
        1022: '苏州研发中心',
        1023: '天津移动',
        1024: '铁通公司',
        1025: '投资公司筹备组',
        1026: '西藏移动',
        1027: '销售分公司',
        1028: '辛姆巴科公司',
        1029: '新疆移动',
        1030: '信息安全管理与运行中心',
        1031: '信息港中心',
        1032: '研究院',
        1033: '移动学院',
        1034: '云南移动',
        1035: '在线服务公司',
        1036: '浙江移动',
        1037: '中国移动通信集团有限公司',
        1038: '中国移动有限公司',
        1039: '中移动金融科技有限公司',
        1040: '中移集成公司',
        1041: '中移物联网有限公司',
        1042: '中移信息技术有限公司',
        1043: '终端公司',
        1044: '重庆移动',
        1045: '卓望公司',
        1046: '安徽移动',
        1047: '北京移动',
        1048: '财务公司',
        1049: '采购共享服务中心',
        1050: '成都产业研究院',
        1051: '福建移动',
        1052: '甘肃移动',
        1053: '广西移动',
        1054: '海南移动',
        1055: '湖北移动',
        1056: '湖南移动'
      }
      return company[code]?company[code]:""
    }
    openApp (options:{
      downLoadUrl?:string,
      isWeChat?:Function,
      openApp?:any
    }) {

      //判断设备是安卓还是苹果系统
      var UA = window.navigator.userAgent;
      var _openAppUrl = function () { };
      var isAndroid = UA.indexOf('Android') > -1 || UA.indexOf('Adr') > -1;
      var isiOS = /(iPhone|iPad|iPod|iOS)/i.test(UA);
      var isWeChat = /MicroMessenger/i.test(UA);
      var downLoadUrl = options.downLoadUrl || 'http://y.10086.cn/x/julb';
    
      //如果是在微信内部点击的时候
      if (isWeChat) {
        if (options.isWeChat) {
          options.isWeChat()
        } else {
          window.location.href = downLoadUrl
        }
      } else if (isiOS || isAndroid) {
        //使用计算时差的方式判断是否已经拉起app
        var checkOpen = function (callback:Function) {
          var _clickTime = +(new Date()),
            _count = 0,
            intHandle:any = 0;
          //启动间隔20ms运行的定时器，并检测累计消耗时间是否超过设定值，超过则结束
          //计时间 = 125*20ms，且必须小于5000ms。
          intHandle = setInterval(function () {
            _count++;
            var elsTime = +(new Date()) - _clickTime;
            if (_count >= 125 || elsTime > 5000) {
              clearInterval(intHandle);
              //计算结束，根据不同，做不同的跳转处理，0表示已经跳转APP成功了
              if (elsTime > 5000 || document.hidden) {
                callback(0);
              } else {
                callback(1);
              }
            }
          }, 20);
        }
        _openAppUrl = function () {
          //点击后同时进行本地app拉起与网页转跳行为
          options?.openApp()
          //点击按钮后2.5秒没有转跳到app则转跳下载链接
          checkOpen(function (opened:number|string) {
            //跳转app失败
            if (opened === 1) {
              location.href = downLoadUrl;
            }
          });
        }
        _openAppUrl();
      } else {
        //PC端或其他设备
        window.location.href = downLoadUrl
      }
    }

    /** 判断当前时间是否在某个时间之前 */
    isBefore(date: string | number | Date | dayjs.Dayjs | null | undefined){
      return dayjs().isBefore(dayjs(date))
    }
    /** 判断当前时间是否在某个时间之后 */
    isAfter(date: string | number | Date | dayjs.Dayjs | null | undefined){
        return dayjs().isAfter(dayjs(date))
    }
    /** 判断某个日期距离今天还剩多少毫秒数 */
    getLeftTime(date: string | number | Date | dayjs.Dayjs | null | undefined){
        return dayjs(date).diff(dayjs())
    }
    /** 将天数和小时数合计为总小时数 */
    daySumHour(day: number, hour = 0){
        return day * 24 + hour
    }
    /** 唯一值创建器 */
    onlyKeyCreator(){
        let str = Math.random().toString(36).substr(3)
        str += Date.now().toString(16).substr(4)
        return str
    }
    // 删除查询参数
    searchValueDel (name: string, href: string) {
        let loca = href || window.location.href
        let query = loca.split('#')[0].split('?')[1] || ''
        let isHash = ''
        if (loca.indexOf('#') > -1) {
        isHash = '#'
        } else {
        isHash = ''
        }
        if (!query) {
        return loca
        }
        if (loca.indexOf(name) > -1) {
        let obj:any = {}
        console.log('query', query)
        let arr:any = query.indexOf('&') > -1 ? query.split('&') : [query]
        for (let i = 0; i < arr.length; i++) {
                arr[i] = arr[i].split('=')
                obj[arr[i][0]] = arr[i][1]
        }
        delete obj[name]
        loca =
            loca.split('#')[0].split('?')[0] +
            (query && Object.keys(obj).length > 0 ? '?' : '') +
            JSON.stringify(obj)
            .replace(/[\"\{\}]/g, '')
            .replace(/\:/g, '=')
            .replace(/\,/g, '&') +
            isHash +
            (isHash ? loca.split('#')[1] : '')
            return loca
        } else {
            return loca
        }
    }
    // 获取地址栏参数-从hash读取
    hashValueDel (name: string, href: string) {
        let loca = href || window.location.href
        let query = ''
        if (loca.indexOf('#') > -1) {
        query = loca.split('#')[1].split('?')[1] || ''
        if (!query) {
                return loca
        }
        } else {
        return loca
        }
        if (loca.indexOf(name) > -1) {
        let obj:any = {}
        let arr:any = query.indexOf('&') > -1 ? query.split('&') : [query]
        for (let i = 0; i < arr.length; i++) {
                arr[i] = arr[i].split('=')
                obj[arr[i][0]] = arr[i][1]
        }
        delete obj[name]
        // console.log(343, loca.substring(0, loca.lastIndexOf('?') + 1))
        loca = loca.substring(0, loca.lastIndexOf('?')) +
            (query && Object.keys(obj).length > 0 ? '?' : '') +
            JSON.stringify(obj)
            .replace(/[\"\{\}]/g, '')
            .replace(/\:/g, '=')
            .replace(/\,/g, '&')
        return loca
        } else {
        return loca
        }
    }
    // 删除链接指定参数值
    delQueStr(name: any, href: any){
        let newurl = this.hashValueDel(name, this.searchValueDel(name, href))
        return newurl
    }
    // 睡眠一段时间
    sleep(time: any){
        return new Promise((resolve) => {
            setTimeout(() => {
                resolve(true)
            }, time || 1000)
        })
    }
    /** 导入某个目录所有的文件 */
    requireAll ( context: { (arg0: any): any; keys: () => any[]; }){
        return context.keys().map((item: any) => {
            return {
                path: item,
                module: context(item)
            }
        })
    }
    //向当前url添加参数
    updateQuery(key: any,val: any,hasHistory=true){
        function updateQueryStringParameter(uri: string, key: string, value: string) {
            if(!value) {
                return uri;
            }
            var re = new RegExp("([?&])" + key + "=.*?(&|$)", "i");
            var separator = uri.indexOf('?') !== -1 ? "&" : "?";
            if (uri.match(re)) {
                return uri.replace(re, '$1' + key + "=" + value + '$2');
            }
            else {
                return uri + separator + key + "=" + value;
            }
        }
        let newurl = updateQueryStringParameter(window.location.href, key, val);
        hasHistory?
        window.history.pushState({
            path: newurl
        }, '', newurl)
        :
        window.history.replaceState({
            path: newurl
        }, '', newurl);
    }
    // 下载文件
    fileLinkToStreamDownload(url:string, fileName:string, type?:string) {
        let reg = /^([hH][tT]{2}[pP]:\/\/|[hH][tT]{2}[pP][sS]:\/\/)(([A-Za-z0-9-~]+).)+([A-Za-z0-9-~\/])+$/;
        const ie = navigator.userAgent.match(/MSIE\s([\d.]+)/),
        ie11 = navigator.userAgent.match(/Trident\/7.0/) && navigator.userAgent.match(/rv:11/),
        ieEDGE = navigator.userAgent.match(/Edge/g),
        ieVer = (ie ? ie[1] : (ie11 ? 11 : (ieEDGE ? 12 : -1)))
        console.log('ie:' + ie)
        console.log('ieVer:' + ieVer)
        if (ie && ieVer < 10) {
            throw new Error('No blobs on IE<10');
            return;
        }
        if (!reg.test(url)) {
            throw new Error("传入参数不合法,不是标准的文件链接");
        } else {
            let xhr = new XMLHttpRequest();
            xhr.open('get', url, true);
            // xhr.setRequestHeader('Content-Type', `application/${type}`);
            xhr.responseType = "blob";
            xhr.onload = function () {
                if (this.status == 200) {
                    //接受二进制文件流
                    console.log(this)
                    var blob = this.response;
                    const blobUrl = window.URL.createObjectURL(blob);
                    if (ieVer > -1) {
                        ((window.navigator) as any).msSaveBlob(blob,fileName);
                    }else{
                        // 这里的文件名根据实际情况从响应头或者url里获取
                        const a = document.createElement('a');
                        a.href = blobUrl;
                        a.download = fileName;
                        a.click();
                        window.URL.revokeObjectURL(blobUrl);
                    }
                }
            }
            xhr.send();
        }
    }
    BlobToStreamDownload(blob:Blob,fileName:string){
        const ie = navigator.userAgent.match(/MSIE\s([\d.]+)/),
        ie11 = navigator.userAgent.match(/Trident\/7.0/) && navigator.userAgent.match(/rv:11/),
        ieEDGE = navigator.userAgent.match(/Edge/g),
        ieVer = (ie ? ie[1] : (ie11 ? 11 : (ieEDGE ? 12 : -1)))
        console.log('ie:' + ie)
        console.log('ieVer:' + ieVer)
        if (ie && ieVer < 10) {
            throw new Error('No blobs on IE<10');
            return;
        }
        //接受二进制文件流
        const blobUrl = window.URL.createObjectURL(blob);
        if (ieVer > -1) {
            ((window.navigator) as any).msSaveBlob(blob,fileName);
        }else{
            // 这里的文件名根据实际情况从响应头或者url里获取
            const a = document.createElement('a');
            a.href = blobUrl;
            a.download = fileName;
            a.style.display = 'none'
            document.body.appendChild(a)
            a.click();
            window.URL.revokeObjectURL(blobUrl);
            document.body.removeChild(a)
        }
    }
    // 深度合并
    deepMerge (target, source) {
        if (isObject(target) && isObject(source)) {
            // 遍历源对象，将源对象存在但目标对象不存在的属性赋值给目标对象
            for (const key in source) {
            if (!target.hasOwnProperty(key)) {
                target[key] = source[key];
            }
            }
            // 遍历目标对象
            for (const key in target) {
                if (source.hasOwnProperty(key)) {
                    // 判断属性值数据类型，若不是对象类型则赋值给目标对象
                    if (!isObject(source[key])) {
                    target[key] = source[key];
                    } else {
                    // 是对象类型则递归调用
                    this.deepMerge(target[key], source[key]);
                    }
                }
            }
        }
    }
    // 首字母大写
    titleCase(str: string) {
        let newStr = str.slice(0, 1).toUpperCase() + str.slice(1).toLowerCase()
        return newStr
    }
    isObject(target: Object) {
        return isObject(target)
    }
}

export default new utils()