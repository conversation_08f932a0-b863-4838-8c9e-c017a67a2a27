<template>
  <div class="dialog-wrap" :class="{ 'dialog-wrap-pc': appStore.isPC }">
    <img v-if="appStore.isPC" class="closePic" @click="emit('close')" :src="$imgs[`close1.png`]" alt="" />
    <slot></slot>
    <button v-if="!appStore.isPC" class="close" @click="emit('close')"></button>
  </div>
</template>
<script setup lang="ts">
import { onMounted, onUnmounted } from 'vue'
import { useAppStore } from '@/store'
const appStore = useAppStore()

onMounted(() => {
  document.body.style.overflow = 'hidden'
})
onUnmounted(() => {
  document.body.style.overflow = 'auto'
})
const emit = defineEmits(['close'])
</script>
<style scoped lang="less">
@import './dialogs/pc.less';

.dialog-wrap {
  position: relative;

  .close {
    width: 48px;
    height: 48px;
    background: url('@/assets/imgs/close.png') no-repeat;
    background-size: 100% auto;
    position: absolute;
    left: 0;
    right: 0;
    margin: auto;
    border: transparent;
    bottom: -80px;
  }

  .closePic {
    position: absolute;
    right: 24px;
    top: 24px;
    width: 50px;
    height: 50px;
    cursor: pointer;
    z-index: 99;
  }
}
</style>
