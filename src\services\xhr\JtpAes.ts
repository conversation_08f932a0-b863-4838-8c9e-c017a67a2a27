import CryptoJS from 'crypto-js'

let secretKey = 'test1'
let secretVal = '12345678'
let encryptType = '01'// 01：AES加密方式

function init(key:string,val:string,type:string){
	secretKey = key
	secretVal = val
	encryptType = type
}

function getSecret () {
	let timestamp = new Date().getTime().toString()
	let fix = Math.floor(Math.random() * 900 + 100).toString()// 100-999的随机数
	let salt = timestamp + fix
	let secret = secretVal + salt
	let key = CryptoJS.MD5(secret).toString().toLowerCase().slice(8, 24)
	let sec = window.btoa(secretKey + ';' + salt + ';' + encryptType)
	return {
		key,
		sec
	}
}

function getKey (sec:any) {
	let key = ''
	if (!sec) {
		return key
	}
	let text = window.atob(sec)
	let data = text.split(';')
	if (data && data.length === 3) {
		let secret = secretVal + data[1]
		key = CryptoJS.MD5(secret).toString().toLowerCase().slice(8, 24)
	}
	return key
}

function encode (params:any) {
	let data = {
		sec: '',
		body: ''
	}
	if (!params || typeof params !== 'object') {
		return data
	}
	let secret = getSecret()
	let word = JSON.stringify(params)
	let key = CryptoJS.enc.Utf8.parse(secret.key)
	let result = CryptoJS.AES.encrypt(word, key, {
		mode: CryptoJS.mode.ECB,
		padding: CryptoJS.pad.Pkcs7
	}).toString()
	data.sec = secret.sec
	data.body = result
	return data
}

function decode (params:any) {
	if (!params || !params.sec || !params.body) {
		return ''
	}
	let key = CryptoJS.enc.Utf8.parse(getKey(params.sec))
	let origin = CryptoJS.AES.decrypt(params.body, key, {
		mode: CryptoJS.mode.ECB,
		padding: CryptoJS.pad.Pkcs7
	})
	const data = origin.toString(CryptoJS.enc.Utf8)
	try {
		let result = JSON.parse(data)
		return result
	} catch {
		return data
	}
}

export { encode, decode,init }