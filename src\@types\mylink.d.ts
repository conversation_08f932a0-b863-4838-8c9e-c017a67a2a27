/** mylink 信息 */
declare namespace Mylink {
	/** 用户信息 */
	export interface UserInfo {
		authorization: string;
		user?: {
			coin?: number;
			createTime?: number;
			email?: string;
			experience?: number;
			headLogo?: string;
			integral?: number;
			is_binded_account?: boolean;
			is_hkid_used?: boolean;
			is_mysim_user?: boolean;
			lastLoginTime?: number;
			membership_user_level?: number;
			phone?: string;
			productId?: string;
			registerType?: number;
			state?: number;
			updateTime?: number;
			userId?: string;
			userType?: number;
			username?: string;
			version?: string;
			nickname?: string;
		};
	}
	/** 设备信息 */
	export interface DeviceInfo {
		appVersion?: string;
		device?: string;
		deviceId?: string;
		network?: string;
		os?: string;
		phone?: string;
		platform?: string;
		resolution?: string;
		userType?: string;
		version?: string;
	}
}
