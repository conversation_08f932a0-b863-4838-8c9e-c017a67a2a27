<template>
  <div class="chat" @click="goUrl(`https://www.hk.chinamobile.com/${lang}/livechatTransfer`)">
    <img :src="$imgs[`chat-${lang}.png`]" />
  </div>
</template>

<script lang="ts" setup>
import { useLang } from '@/hook'
import { ref } from 'vue'
const { lang } = useLang()

const goUrl = (url: string) => {
  if (!appStore.isDecEnv) {
    if (inApp.value) {
      location.href = `openurl-modal://${url}`
    } else {
      window.open(url)
    }
  } else {
    window.open(url)
  }
}
</script>
<style scoped lang="less">
.chat {
  position: fixed;
  bottom: 5dvh;
  right: 36px;
  z-index: 999;
  width: 140px;
  img {
    width: 100%;
    height: auto;
  }
}
.pc {
  .chat {
    position: fixed;
    bottom: 8vh;
    right: 3vw;
    z-index: 999;
    width: 100pw;
    height: auto;
    img {
      width: 100%;
      height: auto;
    }
  }
}
</style>
