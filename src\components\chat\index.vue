<template>
  <div class="chat" @click="goUrl(`https://www.hk.chinamobile.com/${lang}/livechatTransfer`)">
    <img :src="$imgs[`chat-${lang}.png`]" />
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { useLang } from '@/hook'
import { inApp } from '@via/mylink-sdk'
import { useAppStore } from '@/store'

const { lang } = useLang()
const appStore = useAppStore()

const goUrl = (url: string) => {
  if (!appStore.isDecEnv) {
    if (inApp.value) {
      location.href = `openurl-modal://${url}`
    } else {
      window.open(url)
    }
  } else {
    window.open(url)
  }
}
</script>
<style scoped lang="less">
.chat {
  position: fixed;
  bottom: 5dvh;
  right: 36px;
  z-index: 999;
  width: 140px;
  img {
    width: 100%;
    height: auto;
  }
}
.pc {
  .chat {
    position: fixed;
    bottom: 8vh;
    right: 3vw;
    z-index: 999;
    width: 100pw;
    height: auto;
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
      transform: scale(1.1);
      filter: brightness(1.1);
    }

    img {
      width: 100%;
      height: auto;
      transition: all 0.3s ease;
    }
  }
}
</style>
